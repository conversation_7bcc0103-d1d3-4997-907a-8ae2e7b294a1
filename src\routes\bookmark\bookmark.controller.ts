import { Request, Response } from 'express';
import { apiConfig } from '../../config/apiConfig';
import { BookmarkService } from './bookmark.service';

interface AuthenticatedRequest extends Request {
    user_id?: string;
}

export const getBookmarksByCode = async (req: AuthenticatedRequest, res: Response) => {
    try {
        const userId = req.user_id;
        console.log("req", req.url);
        console.log("user id", userId);
        const { code } = req.query;

        if (!userId) {
            res.status(401).json({
                success: false,
                code: apiConfig.code.unauthorized,
                error: 'User ID is required'
            });
        }

        if (!code || typeof code !== 'string') {
            res.status(400).json({
                success: false,
                code: apiConfig.code.error,
                error: 'Code parameter is required'
            });
        }

        const bookmarks = await BookmarkService.getBookmarksByCode(parseInt(userId || '0'), code as string);

        res.json({
            success: true,
            code: apiConfig.code.success,
            data: bookmarks
        });
    } catch (error) {
        console.error('Error fetching bookmarks:', error);
        res.status(500).json({
            success: false,
            code: apiConfig.code.error,
            error: 'Failed to fetch bookmarks'
        });
    }
};

export const createBookmark = async (req: AuthenticatedRequest, res: Response) => {
    try {
        const userId = req.user_id;
        const { code, qna_id } = req.body;

        if (!userId) {
                res.status(401).json({
                    success: false,
                code: apiConfig.code.unauthorized,
                error: apiConfig.codeMessage.unauthorized
            });
        }

        const bookmark = await BookmarkService.createBookmark(parseInt(userId || '0'), code, qna_id);

        res.json({
            success: true,
            code: apiConfig.code.success,
            data: bookmark
        });
    } catch (error) {
        console.error('Controller Error creating bookmark:', error);
        res.status(500).json({
            success: false,
            code: apiConfig.code.error,
            error: 'Failed to add bookmark'
        });
    }
}

export const deleteBookmark = async (req: AuthenticatedRequest, res: Response) => {
    try {
        const userId = req.user_id;
        const { code, qna_id } = req.body;

        if (!userId) {
            res.status(401).json({
                success: false,
                code: apiConfig.code.unauthorized,
                error: apiConfig.codeMessage.unauthorized
            });
        }

        const result = await BookmarkService.deleteBookmark(parseInt(userId || '0'), code, qna_id);  

        res.json({
            success: true,
            code: apiConfig.code.success,
            data: result
        });
    } catch (error) {
        console.error('Error deleting bookmark:', error);
        res.status(500).json({
            success: false,
            code: apiConfig.code.error,
            error: 'Failed to delete bookmark'
        });
    }
}

