import { Request, Response } from 'express';
import { apiConfig } from '../../config/apiConfig';
import { ExamService } from './exam.service';

interface AuthenticatedRequest extends Request {
  user_id?: number;
}

export const getVendorList = async (req: Request, res: Response) => {
  const searchKeyword = req.body.search_keyword;
  const vendors = await ExamService.getVendorList(searchKeyword);
  if (!vendors) {
    res.json({
      success: false,
      code: apiConfig.code.notFound,
      error: apiConfig.codeMessage.notFound
    });
    return;
  }
  res.json({
    success: true,
    code: apiConfig.code.success,
    data: vendors
  });
}

export const getExamList = async (req: Request, res: Response) => {
  try {
    // 明确query参数类型
    const vendor_code = typeof req.query.vendor_code === 'string'
      ? req.query.vendor_code
      : '';

    const result = await ExamService.getExamList(vendor_code);

    res.json({
      success: true,
      code: apiConfig.code.success,
      data: result
    });
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    res.status(500).json({
      success: false,
      code: apiConfig.code.error,
      error: errorMessage
    });
  }
};

export const getExamByCode = async (req: Request, res: Response) => {
  try {
    const { code } = req.params;

    // Your logic to fetch exam by code
    const exam = await ExamService.getExamByCode(code);

    if (!exam) {
      res.json({
        success: false,
        code: apiConfig.code.notFound,
        error: apiConfig.codeMessage.notFound
      });
      return;
    }

    exam.id = exam._id;
    delete exam._id;

    res.json({
      success: true,
      code: apiConfig.code.success,
      data: exam
    });
  } catch (error) {
    console.error('Error fetching exam:', error);
    res.status(500).json({
      success: false,
      code: apiConfig.code.error,
      error: 'Internal server error'
    });
  }
};

export const submitExam = async (req: AuthenticatedRequest, res: Response) => {
  const userId = req.user_id;
  const { code, user_answers, start_time, end_time } = req.body;

  const result = await ExamService.submitExam(code, user_answers, start_time, end_time);
  const arrRsStatistic = JSON.parse(JSON.stringify(result.result_qna));
  delete result.result_qna;
  const rsData = {
    ...result,
    user: userId
  }

  const insertedResult = await ExamService.saveExamResult(rsData);
  console.log("insertedResult: ", JSON.stringify(insertedResult));
  const examResultId = insertedResult.insertedId.toString();
  arrRsStatistic.map((item: any) => {
    item.exam_result_id = examResultId;
    item.user = parseInt(userId + "");
  })
  await ExamService.updateAnswerStatistics(arrRsStatistic);

  res.json({
    success: true,
    code: apiConfig.code.success,
    data: rsData
  });
}

export const searchExam = async (req: Request, res: Response) => {
  const { keyword } = req.body;
  if (!keyword) {
    res.json({
      success: false,
      code: apiConfig.code.badRequest,
      error: "Keyword is required"
    });
    return;
  }

  const exams = await ExamService.searchExam(keyword);

  if (!exams) {
    res.json({
      success: false,
      code: apiConfig.code.notFound,
      error: apiConfig.codeMessage.notFound
    });
    return;
  }

  res.json({
    success: true,
    code: apiConfig.code.success,
    data: exams
  });
}

export const getExamHistory = async (req: AuthenticatedRequest, res: Response) => {
  const userId = req.user_id;
  if (!userId) {
    res.json({
      success: false,
      code: apiConfig.code.badRequest,
      error: "User ID is required"
    });
    return;
  }
  const exams = await ExamService.getExamHistory(userId + "");
  if (!exams) {
    res.json({
      success: false,
      code: apiConfig.code.notFound,
      error: apiConfig.codeMessage.notFound
    });
    return;
  }
  res.json({
    success: true,
    code: apiConfig.code.success,
    data: exams
  });
}

export const getExamResult = async (req: Request, res: Response) => {
  const { result_id } = req.params;
  console.log("result_id", result_id);
  const exam = await ExamService.getExamResult(result_id);
  if (!exam) {
    res.json({
      success: false,
      code: apiConfig.code.notFound,
      error: apiConfig.codeMessage.notFound
    });
    return;
  }
  res.json({
    success: true,
    code: apiConfig.code.success,
    data: exam
  });
}

export const getExamResultStatisticQna = async (req: AuthenticatedRequest, res: Response) => {
  const userId = req.user_id;
  const { code } = req.params;
  const exam = await ExamService.getExamResultStatisticQna(code, parseInt(userId + ""));
  res.json({
    success: true,
    code: apiConfig.code.success,
    data: exam
  });
}