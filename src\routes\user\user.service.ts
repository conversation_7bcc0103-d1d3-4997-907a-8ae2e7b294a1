import { User, IUser } from '../../models/user.model';
import { v4 as uuidv4 } from 'uuid';
import { logger } from '../../utils/logger';

export class UserService {
  /**
   * Create a new user with Google authentication
   */
  async createUserWithGoogle(googleId: string, email: string, displayName: string, profilePicture: string): Promise<IUser> {
    try {
      logger.info('Creating new user with Google authentication', {
        googleId,
        email,
        displayName,
        hasProfilePicture: !!profilePicture
      });

      const newUser = new User({
        googleId,
        email,
        displayName,
        profilePicture,
        // Initialize with empty exam progress
        progress: { exams: {}, lastActivity: new Date() },
        quizResults: [],
        purchases: [],
        aiCredits: { totalCredits: 0, usedCredits: 0, transactions: [] },
        aiChats: [],
        loginHistory: []
      });

      logger.debug('User object before save:', {
        googleId: newUser.googleId,
        email: newUser.email,
        _id: newUser._id,
        isNew: newUser.isNew
      });

      const savedUser = await newUser.save();

      logger.info('User created successfully', {
        userId: savedUser._id,
        googleId: savedUser.googleId,
        email: savedUser.email
      });

      return savedUser;
    } catch (error) {
      logger.error({ error, googleId, email }, 'Error creating user with Google');
      throw error;
    }
  }

  /**
   * Find a user by Google ID
   */
  async findUserByGoogleId(googleId: string): Promise<IUser | null> {
    try {
      logger.debug('Searching for user by Google ID', { googleId });
      const user = await User.findOne({ googleId });

      if (user) {
        logger.debug('User found by Google ID', {
          userId: user._id,
          googleId: user.googleId,
          email: user.email
        });
      } else {
        logger.debug('No user found with Google ID', { googleId });
      }

      return user;
    } catch (error) {
      logger.error({ error, googleId }, 'Error finding user by Google ID');
      throw error;
    }
  }

  /**
   * Get complete user object by Google ID (requires JWT authentication)
   */
  async getUserByGoogleId(googleId: string): Promise<IUser | null> {
    try {
      logger.debug({ googleId }, 'Getting user by Google ID');
      const user = await User.findOne({ googleId });

      if (!user) {
        logger.debug({ googleId }, 'User not found by Google ID');
        return null;
      }

      logger.debug({ userId: user._id, googleId }, 'User found by Google ID');
      return user;
    } catch (error) {
      logger.error({ error, googleId }, 'Error getting user by Google ID');
      throw error;
    }
  }

  /**
   * Find a user by ID
   */
  async findUserById(userId: string): Promise<IUser | null> {
    try {
      return await User.findById(userId);
    } catch (error) {
      logger.error({ error }, 'Error finding user by ID');
      throw error;
    }
  }

  /**
   * Update user profile
   */
  async updateUserProfile(userId: string, profileData: Partial<IUser>): Promise<IUser | null> {
    try {
      // Only allow updating specific fields
      const allowedUpdates = {
        displayName: profileData.displayName,
        profilePicture: profileData.profilePicture,
        updatedAt: new Date()
      };

      return await User.findByIdAndUpdate(
        userId,
        { $set: allowedUpdates },
        { new: true }
      );
    } catch (error) {
      logger.error({ error }, 'Error updating user profile');
      throw error;
    }
  }

  /**
   * Get user progress
   */
  async getUserProgress(userId: string, examId?: string): Promise<any> {
    try {
      logger.debug('Getting user progress', { userId, examId });

      // Try to find user by MongoDB _id first, if that fails, try by Google ID
      let user = await User.findById(userId).catch(() => null);
      if (!user) {
        logger.debug('User not found by MongoDB ID, trying Google ID', { userId });
        user = await User.findOne({ googleId: userId });
      }

      if (!user) {
        logger.warn('User not found when getting progress', { userId });
        return null;
      }

      logger.debug('User found for progress retrieval', {
        userId,
        mongoId: user._id,
        googleId: user.googleId
      });

      if (examId) {
        // Return progress for a specific exam
        const examProgress = user.progress.exams[examId] || {};
        logger.debug('Retrieved exam progress', { userId, examId, examProgress });
        return examProgress;
      }

      // Return all progress
      logger.debug('Retrieved all user progress', { userId, progressKeys: Object.keys(user.progress.exams) });
      return user.progress;
    } catch (error) {
      logger.error({ error }, 'Error getting user progress');
      throw error;
    }
  }

  /**
   * Bulk update user progress for an entire exam
   */
  async updateUserProgressBulk(userId: string, examId: string, progressData: any): Promise<any> {
    try {
      logger.debug('Bulk updating user progress', { userId, examId, progressData });

      // Try to find user by MongoDB _id first, if that fails, try by Google ID
      let user = await User.findById(userId).catch(() => null);
      if (!user) {
        logger.debug('User not found by MongoDB ID, trying Google ID', { userId });
        user = await User.findOne({ googleId: userId });
      }

      if (!user) {
        logger.warn('User not found when bulk updating progress', { userId });
        return null;
      }

      logger.debug('User found for bulk update', {
        userId,
        mongoId: user._id,
        googleId: user.googleId
      });

      // Initialize exam progress if it doesn't exist
      if (!user.progress.exams[examId]) {
        user.progress.exams[examId] = {};
        logger.debug('Initialized exam progress for bulk update', { userId, examId });
      }

      // Save the input data directly without validation or transformation
      user.progress.exams[examId] = progressData;
      user.progress.lastActivity = new Date();

      // Mark the progress field as modified for Mongoose to detect changes
      user.markModified('progress');

      // Save the user with detailed logging
      logger.debug('Saving bulk user progress to database', {
        userId,
        examId,
        progressData
      });

      await user.save();

      logger.debug('Bulk user progress saved successfully', { userId, examId });
      return user.progress.exams[examId];
    } catch (error) {
      logger.error({ error }, 'Error bulk updating user progress');
      throw error;
    }
  }

  /**
   * Update user progress for exam questions
   */
  async updateUserProgress(userId: string, examId: string, subject: string, progressData: any): Promise<any> {
    try {
      logger.debug('Updating user progress', { userId, examId, subject, progressData });

      // Try to find user by MongoDB _id first, if that fails, try by Google ID
      let user = await User.findById(userId).catch(() => null);
      if (!user) {
        logger.debug('User not found by MongoDB ID, trying Google ID', { userId });
        user = await User.findOne({ googleId: userId });
      }

      if (!user) {
        logger.warn('User not found when updating progress', { userId });
        return null;
      }

      logger.debug('User found for progress update', {
        userId,
        mongoId: user._id,
        googleId: user.googleId
      });

      // Initialize exam progress if it doesn't exist
      if (!user.progress.exams[examId]) {
        user.progress.exams[examId] = {};
        logger.debug('Initialized exam progress', { userId, examId });
      }

      // Initialize subject progress if it doesn't exist
      if (!user.progress.exams[examId][subject]) {
        user.progress.exams[examId][subject] = {
          browsed: [],
          bookmarked: [],
          incorrect: [],
          correct: []
        };
        logger.debug('Initialized subject progress', { userId, examId, subject });
      }

      const subjectProgress = user.progress.exams[examId][subject];
      const timestamp = new Date();

      // Update progress based on the action type
      if (progressData.questionId) {
        const progressItem = { id: progressData.questionId, timestamp };

        if (progressData.action === 'browsed') {
          // Remove existing entry if it exists and add new one
          subjectProgress.browsed = subjectProgress.browsed.filter(item => item.id !== progressData.questionId);
          subjectProgress.browsed.push(progressItem);
          logger.debug('Updated browsed progress', { userId, examId, subject, questionId: progressData.questionId });
        }

        if (progressData.action === 'bookmarked') {
          if (progressData.isBookmarked) {
            // Add bookmark if not already present
            if (!subjectProgress.bookmarked.find(item => item.id === progressData.questionId)) {
              subjectProgress.bookmarked.push(progressItem);
              logger.debug('Added bookmark', { userId, examId, subject, questionId: progressData.questionId });
            }
          } else {
            // Remove bookmark
            subjectProgress.bookmarked = subjectProgress.bookmarked.filter(item => item.id !== progressData.questionId);
            logger.debug('Removed bookmark', { userId, examId, subject, questionId: progressData.questionId });
          }
        }

        if (progressData.action === 'answered') {
          const questionId = progressData.questionId;

          // Remove from both correct and incorrect first
          subjectProgress.correct = subjectProgress.correct.filter(item => item.id !== questionId);
          subjectProgress.incorrect = subjectProgress.incorrect.filter(item => item.id !== questionId);

          // Add to appropriate category
          if (progressData.isCorrect) {
            subjectProgress.correct.push(progressItem);
            logger.debug('Added to correct answers', { userId, examId, subject, questionId });
          } else {
            subjectProgress.incorrect.push(progressItem);
            logger.debug('Added to incorrect answers', { userId, examId, subject, questionId });
          }
        }
      }

      // Update last activity
      user.progress.lastActivity = timestamp;

      // Mark the progress field as modified for Mongoose to detect changes
      user.markModified('progress');

      // Save the user with detailed logging
      logger.debug('Saving user progress to database', {
        userId,
        examId,
        subject,
        browsedCount: subjectProgress.browsed.length,
        bookmarkedCount: subjectProgress.bookmarked.length,
        correctCount: subjectProgress.correct.length,
        incorrectCount: subjectProgress.incorrect.length
      });

      await user.save();

      logger.debug('User progress saved successfully', { userId, examId, subject });
      return user.progress.exams[examId][subject];
    } catch (error) {
      logger.error({ error }, 'Error updating user progress');
      throw error;
    }
  }

  /**
   * Get quiz results
   */
  async getQuizResults(userId: string, quizId?: string): Promise<any> {
    try {
      const user = await User.findById(userId);
      if (!user) return null;

      if (quizId) {
        // Return results for a specific quiz
        const quizResult = user.quizResults.find(
          result => result.quizId === quizId
        );
        return quizResult || null;
      }

      // Return all quiz results
      return user.quizResults;
    } catch (error) {
      logger.error({ error }, 'Error getting quiz results');
      throw error;
    }
  }

  /**
   * Submit quiz result
   */
  async submitQuizResult(userId: string, quizResult: any): Promise<any> {
    try {
      const user = await User.findById(userId);
      if (!user) return null;

      // Add completion timestamp if not provided
      if (!quizResult.completedAt) {
        quizResult.completedAt = new Date();
      }

      // Check if this quiz has been taken before
      const existingIndex = user.quizResults.findIndex(
        result => result.quizId === quizResult.quizId
      );

      if (existingIndex >= 0) {
        // Replace existing result
        user.quizResults[existingIndex] = quizResult;
      } else {
        // Add new result
        user.quizResults.push(quizResult);
      }

      await user.save();
      return existingIndex >= 0 ? user.quizResults[existingIndex] : user.quizResults[user.quizResults.length - 1];
    } catch (error) {
      logger.error({ error }, 'Error submitting quiz result');
      throw error;
    }
  }

  /**
   * Get user purchases
   */
  async getUserPurchases(userId: string, status?: string): Promise<any> {
    try {
      const user = await User.findById(userId);
      if (!user) return null;

      if (status) {
        // Return purchases with specific status
        return user.purchases.filter(purchase => purchase.status === status);
      }

      // Return all purchases
      return user.purchases;
    } catch (error) {
      logger.error({ error }, 'Error getting user purchases');
      throw error;
    }
  }

  /**
   * Verify and record a purchase
   */
  async verifyPurchase(userId: string, purchaseData: any): Promise<any> {
    try {
      const user = await User.findById(userId);
      if (!user) return null;

      // Check if transaction already exists
      const existingPurchase = user.purchases.find(
        purchase => purchase.transactionId === purchaseData.transactionId
      );

      if (existingPurchase) {
        return { success: false, message: 'Transaction already processed' };
      }

      // In a real implementation, you would verify the receipt with Apple/Google/etc.
      // For now, we'll just record the purchase

      // Add purchase to user's purchases
      const purchase = {
        ...purchaseData,
        purchasedAt: new Date(),
        expiresAt: purchaseData.expiresAt || new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // Default to 1 year
        status: 'active'
      };

      user.purchases.push(purchase);
      await user.save();

      return { success: true, purchase };
    } catch (error) {
      logger.error({ error }, 'Error verifying purchase');
      throw error;
    }
  }

  /**
   * Get AI credits
   */
  async getAICredits(userId: string): Promise<any> {
    try {
      const user = await User.findById(userId);
      if (!user) return null;

      return user.aiCredits;
    } catch (error) {
      logger.error({ error }, 'Error getting AI credits');
      throw error;
    }
  }

  /**
   * Get AI credit transactions
   */
  async getAICreditTransactions(userId: string, limit: number, offset: number): Promise<any> {
    try {
      const user = await User.findById(userId);
      if (!user) return null;

      // Sort transactions by timestamp (newest first) and apply pagination
      const sortedTransactions = [...user.aiCredits.transactions]
        .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
        .slice(offset, offset + limit);

      return {
        transactions: sortedTransactions,
        total: user.aiCredits.transactions.length
      };
    } catch (error) {
      logger.error({ error }, 'Error getting AI credit transactions');
      throw error;
    }
  }

  /**
   * Purchase AI credits
   */
  async purchaseAICredits(userId: string, purchaseData: any): Promise<any> {
    try {
      // Try to find user by MongoDB _id first, if that fails, try by Google ID
      let user = await User.findById(userId).catch(() => null);
      if (!user) {
        user = await User.findOne({ googleId: userId });
      }
      if (!user) return null;

      // Add credits to user's account
      user.aiCredits.totalCredits += purchaseData.amount;

      // Record transaction
      const transaction = {
        amount: purchaseData.amount,
        reason: 'purchase',
        timestamp: new Date(),
        relatedEntityId: purchaseData.paymentDetails?.transactionId
      };

      user.aiCredits.transactions.push(transaction);
      user.markModified('aiCredits');
      await user.save();

      return user.aiCredits;
    } catch (error) {
      logger.error({ error }, 'Error purchasing AI credits');
      throw error;
    }
  }

  /**
   * Get AI chat history
   * Returns the complete AI chat history organized by question IDs
   * Automatically migrates from old concatenated key format to new nested structure
   */
  async getAIChatHistory(userId: string): Promise<any> {
    try {
      // Try to find user by MongoDB _id first, if that fails, try by Google ID
      let user = await User.findById(userId).catch(() => null);
      if (!user) {
        user = await User.findOne({ googleId: userId });
      }
      if (!user) return null;

      let aiChats = user.aiChats as any;

      // Check if we need to migrate the entire structure
      const needsFullMigration = this.checkIfFullMigrationNeeded(aiChats);
      if (needsFullMigration) {
        logger.info('Migrating entire AI chat data structure from concatenated keys to nested format', { userId });
        aiChats = this.migrateAIChatStructure(aiChats);
        user.aiChats = aiChats;
        user.markModified('aiChats');
        await user.save();
        logger.info('AI chat data migration completed', { userId });
      }

      // Return the complete aiChats data in the proper nested format
      return aiChats || {};
    } catch (error) {
      logger.error({ error }, 'Error getting AI chat history');
      throw error;
    }
  }

  /**
   * Update AI chat history
   * Replaces the entire AI chat history with the provided data
   */
  async updateAIChatHistory(userId: string, chatHistoryData: any): Promise<any> {
    try {
      logger.debug('Updating AI chat history', { userId, chatHistoryData });

      // Try to find user by MongoDB _id first, if that fails, try by Google ID
      let user = await User.findById(userId).catch(() => null);
      if (!user) {
        user = await User.findOne({ googleId: userId });
      }
      if (!user) return null;

      // Replace the entire aiChats data with the incoming data
      user.aiChats = chatHistoryData;
      user.markModified('aiChats');

      await user.save();

      logger.debug('AI chat history updated successfully', { userId });
      return user.aiChats;
    } catch (error) {
      logger.error({ error }, 'Error updating AI chat history');
      throw error;
    }
  }

  /**
   * Get a specific AI chat
   */
  async getAIChat(userId: string, chatId: string): Promise<any> {
    try {
      // Try to find user by MongoDB _id first, if that fails, try by Google ID
      let user = await User.findById(userId).catch(() => null);
      if (!user) {
        user = await User.findOne({ googleId: userId });
      }
      if (!user) return null;

      const chat = user.aiChats.find(chat => chat.chatId === chatId);
      return chat || null;
    } catch (error) {
      logger.error({ error }, 'Error getting AI chat');
      throw error;
    }
  }

  /**
   * Create a new AI chat
   */
  async createAIChat(userId: string, title?: string): Promise<any> {
    try {
      // Try to find user by MongoDB _id first, if that fails, try by Google ID
      let user = await User.findById(userId).catch(() => null);
      if (!user) {
        user = await User.findOne({ googleId: userId });
      }
      if (!user) return null;

      const newChat = {
        chatId: uuidv4(),
        title: title || 'New Chat',
        createdAt: new Date(),
        updatedAt: new Date(),
        messages: []
      };

      user.aiChats.push(newChat);
      user.markModified('aiChats');
      await user.save();

      return newChat;
    } catch (error) {
      logger.error({ error }, 'Error creating AI chat');
      throw error;
    }
  }

  /**
   * Send a message in an AI chat
   */
  async sendAIChatMessage(userId: string, chatId: string, message: string): Promise<any> {
    try {
      // Try to find user by MongoDB _id first, if that fails, try by Google ID
      let user = await User.findById(userId).catch(() => null);
      if (!user) {
        user = await User.findOne({ googleId: userId });
      }
      if (!user) return null;

      const chatIndex = user.aiChats.findIndex(chat => chat.chatId === chatId);
      if (chatIndex === -1) return null;

      // Add user message
      const userMessage = {
        role: 'user' as const,
        content: message,
        timestamp: new Date(),
        tokensUsed: 0 // Calculate tokens if needed
      };

      user.aiChats[chatIndex].messages.push(userMessage);

      // In a real implementation, you would call an AI service here
      // For now, we'll just simulate an AI response
      const aiResponse = {
        role: 'assistant' as const,
        content: `This is a simulated response to: "${message}"`,
        timestamp: new Date(),
        tokensUsed: 10 // Simulated token usage
      };

      user.aiChats[chatIndex].messages.push(aiResponse);

      // Update chat metadata
      user.aiChats[chatIndex].updatedAt = new Date();

      // Deduct credits for AI usage
      const tokensUsed = 10; // Simulated token usage
      user.aiCredits.usedCredits += tokensUsed;

      // Record transaction
      user.aiCredits.transactions.push({
        amount: -tokensUsed,
        reason: 'usage',
        timestamp: new Date(),
        relatedEntityId: chatId
      });

      user.markModified('aiChats');
      user.markModified('aiCredits');
      await user.save();

      return user.aiChats[chatIndex];
    } catch (error) {
      logger.error({ error }, 'Error sending AI chat message');
      throw error;
    }
  }

  /**
   * Delete an AI chat
   */
  async deleteAIChat(userId: string, chatId: string): Promise<any> {
    try {
      // Try to find user by MongoDB _id first, if that fails, try by Google ID
      let user = await User.findById(userId).catch(() => null);
      if (!user) {
        user = await User.findOne({ googleId: userId });
      }
      if (!user) return null;

      const chatIndex = user.aiChats.findIndex(chat => chat.chatId === chatId);
      if (chatIndex === -1) return { success: false, message: 'Chat not found' };

      // Remove chat
      user.aiChats.splice(chatIndex, 1);
      user.markModified('aiChats');
      await user.save();

      return { success: true };
    } catch (error) {
      logger.error({ error }, 'Error deleting AI chat');
      throw error;
    }
  }

  /**
   * Add messages to AI chat history for a specific exam and QnA
   * Follows the data.json structure: user.aiChats[examId][qnaId].messages[]
   */
  async addAIChatMessages(
    userId: string,
    examId: string,
    qnaId: string,
    messages: any[],
    quickReplies?: string[]
  ): Promise<any> {
    try {
      logger.debug('Adding AI chat messages to user', {
        userId,
        examId,
        qnaId,
        messageCount: messages.length,
        hasQuickReplies: !!quickReplies
      });

      // Try to find user by MongoDB _id first, if that fails, try by Google ID
      let user = await User.findById(userId).catch(() => null);
      if (!user) {
        user = await User.findOne({ googleId: userId });
      }
      if (!user) {
        logger.warn('User not found for addAIChatMessages', { userId });
        return null;
      }

      // Cast aiChats to any since it's Schema.Types.Mixed and we're using the new structure
      let aiChats = user.aiChats as any;

      // Check if we need to migrate from old concatenated key format to new nested format
      const needsMigration = this.checkIfMigrationNeeded(aiChats, examId, qnaId);
      if (needsMigration) {
        logger.info('Migrating AI chat data from concatenated keys to nested structure', { userId, examId, qnaId });
        aiChats = this.migrateAIChatStructure(aiChats);
        user.aiChats = aiChats;
      }

      // Initialize aiChats structure if it doesn't exist or is array format
      if (!aiChats || Array.isArray(aiChats)) {
        user.aiChats = {} as any;
        aiChats = user.aiChats;
        logger.debug('Initialized new aiChats structure', { examId });
      }

      // Initialize exam structure if it doesn't exist
      if (!aiChats[examId]) {
        aiChats[examId] = {};
        logger.debug('Created new exam structure', { examId });
      }

      // Initialize QnA structure if it doesn't exist
      if (!aiChats[examId][qnaId]) {
        aiChats[examId][qnaId] = {
          messages: [],
          quickReplies: [
            "Explain why the answer is correct and others are wrong.",
            "Give an example to clarify the answer.",
            "Share references for the correct answer."
          ]
        };
        logger.debug('Created new QnA structure', { examId, qnaId });
      }

      // Get current message count before adding new messages
      const currentMessageCount = aiChats[examId][qnaId].messages?.length || 0;

      // Ensure messages array exists
      if (!aiChats[examId][qnaId].messages) {
        aiChats[examId][qnaId].messages = [];
      }

      // Add each message to the array
      for (const message of messages) {
        aiChats[examId][qnaId].messages.push(message);
        logger.debug('Added message to chat history', {
          examId,
          qnaId,
          messageId: message.id,
          role: message.choices?.[0]?.message?.role
        });
      }

      // Update quickReplies if provided
      if (quickReplies && Array.isArray(quickReplies)) {
        aiChats[examId][qnaId].quickReplies = quickReplies;
        logger.debug('Updated quick replies', { examId, qnaId, quickRepliesCount: quickReplies.length });
      }

      // Mark the aiChats field as modified for MongoDB
      user.markModified('aiChats');

      // Save the user document
      await user.save();

      const finalMessageCount = aiChats[examId][qnaId].messages.length;
      const addedCount = finalMessageCount - currentMessageCount;

      logger.info('Successfully added AI chat messages', {
        userId,
        examId,
        qnaId,
        addedCount,
        totalMessages: finalMessageCount
      });

      return {
        messageCount: finalMessageCount,
        addedCount: addedCount
      };
    } catch (error) {
      logger.error({ error }, 'Error adding AI chat messages', { userId, examId, qnaId });
      throw error;
    }
  }

  /**
   * Get login history
   */
  async getLoginHistory(userId: string, limit: number, offset: number): Promise<any> {
    try {
      const user = await User.findById(userId);
      if (!user) return null;

      // Sort login history by timestamp (newest first) and apply pagination
      const sortedHistory = [...user.loginHistory]
        .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
        .slice(offset, offset + limit);

      return {
        history: sortedHistory,
        total: user.loginHistory.length
      };
    } catch (error) {
      logger.error({ error }, 'Error getting login history');
      throw error;
    }
  }

  /**
   * Record login
   */
  async recordLogin(userId: string, loginData: any): Promise<any> {
    try {
      const user = await User.findById(userId);
      if (!user) return null;

      // Add login to history
      user.loginHistory.push({
        timestamp: new Date(),
        device: loginData.device || 'Unknown',
        platform: loginData.platform || 'Unknown',
        ipAddress: loginData.ipAddress || 'Unknown',
        location: loginData.location
      });

      await user.save();
      return { success: true };
    } catch (error) {
      logger.error({ error }, 'Error recording login');
      throw error;
    }
  }

  /**
   * Check if AI chat data needs migration from concatenated keys to nested structure
   */
  private checkIfMigrationNeeded(aiChats: any, examId: string, qnaId: string): boolean {
    if (!aiChats || typeof aiChats !== 'object') {
      return false;
    }

    // Check if we have the old concatenated key format
    const concatenatedKey = `${examId}_${qnaId}`;
    const hasOldFormat = aiChats[concatenatedKey] && typeof aiChats[concatenatedKey] === 'object';

    // Check if we already have the new nested format
    const hasNewFormat = aiChats[examId] && aiChats[examId][qnaId];

    // Need migration if we have old format but not new format
    return hasOldFormat && !hasNewFormat;
  }

  /**
   * Check if the entire AI chat structure needs migration
   */
  private checkIfFullMigrationNeeded(aiChats: any): boolean {
    if (!aiChats || typeof aiChats !== 'object' || Array.isArray(aiChats)) {
      return false;
    }

    // Check if any keys contain underscores (indicating old concatenated format)
    const keys = Object.keys(aiChats);
    const hasOldFormatKeys = keys.some(key => {
      // Check if key contains underscore and the last part looks like a qnaId (24 char hex)
      if (key.includes('_')) {
        const parts = key.split('_');
        const lastPart = parts[parts.length - 1];
        return lastPart.match(/^[a-f0-9]{24}$/);
      }
      return false;
    });

    // Check if we have any properly nested structure
    const hasNestedStructure = keys.some(key => {
      return !key.includes('_') &&
             typeof aiChats[key] === 'object' &&
             !Array.isArray(aiChats[key]) &&
             Object.keys(aiChats[key]).some(subKey => subKey.match(/^[a-f0-9]{24}$/));
    });

    // Need migration if we have old format keys but no proper nested structure
    return hasOldFormatKeys && !hasNestedStructure;
  }

  /**
   * Migrate AI chat data from concatenated keys to nested structure
   * Converts: { "examId_qnaId": {...} } to { "examId": { "qnaId": {...} } }
   */
  private migrateAIChatStructure(aiChats: any): any {
    if (!aiChats || typeof aiChats !== 'object') {
      return {};
    }

    const migratedChats: any = {};

    // Copy existing nested structure if it exists
    for (const key in aiChats) {
      if (key.includes('_')) {
        // This is a concatenated key, parse it
        const parts = key.split('_');
        if (parts.length >= 2) {
          // Handle cases where examId or qnaId might contain underscores
          // Assume the last part is qnaId (24 char hex) and everything before is examId
          const lastPart = parts[parts.length - 1];
          if (lastPart.match(/^[a-f0-9]{24}$/)) {
            // Last part looks like a qnaId
            const qnaId = lastPart;
            const examId = parts.slice(0, -1).join('_');

            if (!migratedChats[examId]) {
              migratedChats[examId] = {};
            }

            migratedChats[examId][qnaId] = aiChats[key];

            logger.debug('Migrated chat data', {
              oldKey: key,
              examId,
              qnaId,
              hasMessages: !!aiChats[key].messages,
              messageCount: aiChats[key].messages?.length || 0
            });
          }
        }
      } else {
        // This might already be in new format, preserve it
        if (typeof aiChats[key] === 'object' && !Array.isArray(aiChats[key])) {
          migratedChats[key] = aiChats[key];
        }
      }
    }

    return migratedChats;
  }
}