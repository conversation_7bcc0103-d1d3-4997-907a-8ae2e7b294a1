import { Request, Response } from 'express';
import { apiConfig } from '../../config/apiConfig';
import { QnAService } from './qna.service';

export const getUserQnaStatus = async (req: Request, res: Response): Promise<void> => {
  try {
      // Validate required parameters
      if (!req.query.user_id || !req.query.exam_code) {
          res.status(400).json({
              success: false,
              code: apiConfig.code.error,
              error: 'user_id and exam_code are required'
          });
          return;
      }

      const result = await QnAService.getUserQnaStatus(
          req.query.user_id as string,
          req.query.exam_code as string,
          req.query.start_date as string | undefined,
          req.query.end_date as string | undefined
      );

      res.json({
          success: true,
          code: apiConfig.code.success,
          data: result
      });
  } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      res.status(500).json({
          success: false,
          code: apiConfig.code.error,
          error: errorMessage
      });
  }
};

// src/routes/qna/qna.controller.ts
export const updateQnaStatus = async (req: Request, res: Response): Promise<void> => {
  try {
      const { user_id, exam_code, qna_ids, is_bookmarked, is_browsed, is_incorrect } = req.body;

      // Validate required parameters
      if (!user_id || !exam_code || !qna_ids?.length) {
          res.status(400).json({
              success: false,
              code: apiConfig.code.error,
              error: 'Missing required parameters: user_id, exam_code, or qna_ids'
          });
          return;
      }

      // Validate at least one status parameter
      if (!is_bookmarked && !is_browsed && !is_incorrect) {
          res.status(400).json({
              success: false,
              code: apiConfig.code.error,
              error: 'At least one status parameter must be specified: is_bookmarked, is_browsed, or is_incorrect'
          });
          return;
      }

      // Process updates
      const updatedCount = await QnAService.updateQnaStatus(
          user_id,
          exam_code,
          qna_ids,
          { is_bookmarked, is_browsed, is_incorrect }
      );

      res.json({
          success: true,
          code: apiConfig.code.success,
          data: { updated_count: updatedCount }
      });
  } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      res.status(500).json({
          success: false,
          code: apiConfig.code.error,
          error: errorMessage
      });
  }
};

export const getQna = async (req: Request, res: Response): Promise<void> => {
  try {
    // Validate required parameter
    if (!req.query.exam_code) {
      res.status(400).json({
        success: false,
        code: apiConfig.code.error,
        error: 'exam_code is required'
      });
      return;
    }

    // Parse is_free parameter (default to false if not provided)
    const isFree = req.query.is_free === 'true';

    const result = await QnAService.getQna(
      req.query.exam_code as string,
      req.query.vendor_code as string | undefined,
      isFree
    );

    res.json({
      success: true,
      code: apiConfig.code.success,
      data: result
    });
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    res.status(500).json({
      success: false,
      code: apiConfig.code.error,
      error: errorMessage
    });
  }
};

export const getQnaCollections = async (req: Request, res: Response) => {
  try {
    // You can now access the authenticated user's ID
    const collections = await QnAService.getQnACollections();
    
    res.json({
      success: true,
      code: apiConfig.code.success,
      data: collections
    });
  } catch (error) {
    console.error('Error fetching QNA collections:', error);
    res.status(500).json({
      success: false,
      code: apiConfig.code.error,
      error: 'Failed to fetch QNA collections'
    });
  }
}; 

export const getQnaByCode = async (req: Request, res: Response) => {
  const { code } = req.params;
  try {
    const qnaData = await QnAService.getQnAByCode(code);
    if (!qnaData) {
      res.status(404).json({
        success: false,
        code: apiConfig.code.error,
        error: 'QnA collection not found'
      });
      return;
    }

    res.json({
      success: true,
      code: apiConfig.code.success,
      data: qnaData
    });
  } catch (error) {
    console.error('Error fetching QnA by code:', error);
    res.status(500).json({
      success: false,
      code: apiConfig.code.error,
      error: 'Failed to fetch QnA by code'
    });
  }
};

export const submitFeedback = async (req: Request, res: Response) => {
  try {
    const { userId, qnaId, content } = req.body;

    // Validate required parameters
    if (!userId || !qnaId || !content) {
      res.status(400).json({
        success: false,
        code: apiConfig.code.error,
        error: 'Missing required parameters: userId, qnaId, or content'
      });
      return;
    }

    await QnAService.submitFeedback(userId, qnaId, content);

    res.json({
      success: true,
      code: apiConfig.code.success,
      data: { message: 'Feedback submitted successfully' }
    });
  } catch (error) {
    console.error('Error submitting feedback:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    res.status(500).json({
      success: false,
      code: apiConfig.code.error,
      error: errorMessage
    });
  }
};

export const getQnaSentence = async (req: Request, res: Response) => {
  const { code, id } = req.params;
  try {
    const qnaData = await QnAService.getQnASentence(code, id);
    if (!qnaData) {
      res.status(404).json({
        success: false,
        code: apiConfig.code.error,
        error: 'QnA sentence not found'
      });
      return;
    }

    const details = qnaData;
    details.id = id;
    delete details._id;

    res.json({
      success: true,
      code: apiConfig.code.success,
      data: details
    });
  } catch (error) {
    console.error('Controller Error fetching QnA sentence:', error); 
    res.status(500).json({
      success: false,
      code: apiConfig.code.error,
      error: 'Failed to fetch QnA sentence'
    });
  }
};