/**
 * Test script to verify compression configuration
 * Run with: node test-compression-config.js
 */

// Simulate environment variables for testing
process.env.ENABLE_COMPRESSION = 'true';
process.env.ENABLE_REQUEST_COMPRESSION = 'true';
process.env.ENABLE_RESPONSE_COMPRESSION = 'true';
process.env.COMPRESSION_THRESHOLD = '5120';

// Import the compression utility (adjust path as needed)
const { CompressionUtil } = require('./src/utils/compression.util');

async function testCompressionConfig() {
  console.log('🧪 Testing Compression Configuration\n');

  // Test 1: Check configuration reading
  console.log('📋 Configuration Status:');
  console.log('  Compression Enabled:', CompressionUtil.isCompressionEnabled());
  console.log('  Request Compression:', CompressionUtil.isRequestCompressionEnabled());
  console.log('  Response Compression:', CompressionUtil.isResponseCompressionEnabled());
  console.log('  Threshold:', CompressionUtil.getCompressionThreshold(), 'bytes');
  console.log('  Threshold (formatted):', CompressionUtil.formatBytes(CompressionUtil.getCompressionThreshold()));
  console.log();

  // Test 2: Test with small data (should not compress)
  console.log('📦 Testing Small Data (should not compress):');
  const smallData = { message: 'Hello World' };
  const shouldCompressSmall = CompressionUtil.shouldCompress(smallData);
  console.log('  Data size:', CompressionUtil.formatBytes(CompressionUtil.getDataSize(smallData)));
  console.log('  Should compress:', shouldCompressSmall);
  
  if (shouldCompressSmall) {
    const compressionResult = await CompressionUtil.compressData(smallData);
    console.log('  Compression ratio:', compressionResult.compressionRatio.toFixed(2) + '%');
  }
  console.log();

  // Test 3: Test with large data (should compress)
  console.log('📦 Testing Large Data (should compress):');
  const largeData = {
    conversations: Array(100).fill(null).map((_, i) => ({
      id: `conv_${i}`,
      messages: Array(50).fill(null).map((_, j) => ({
        id: `msg_${i}_${j}`,
        content: `This is a test message with some content to make it larger. Message ${j} in conversation ${i}. Lorem ipsum dolor sit amet, consectetur adipiscing elit.`,
        timestamp: new Date().toISOString(),
        role: j % 2 === 0 ? 'user' : 'assistant'
      }))
    }))
  };

  const shouldCompressLarge = CompressionUtil.shouldCompress(largeData);
  console.log('  Data size:', CompressionUtil.formatBytes(CompressionUtil.getDataSize(largeData)));
  console.log('  Should compress:', shouldCompressLarge);
  
  if (shouldCompressLarge) {
    const compressionResult = await CompressionUtil.compressData(largeData);
    console.log('  Original size:', CompressionUtil.formatBytes(compressionResult.originalSize));
    console.log('  Compressed size:', CompressionUtil.formatBytes(compressionResult.compressedSize));
    console.log('  Compression ratio:', compressionResult.compressionRatio.toFixed(2) + '%');
    console.log('  Algorithm:', compressionResult.algorithm);
  }
  console.log();

  // Test 4: Test with compression disabled
  console.log('🚫 Testing with Compression Disabled:');
  process.env.ENABLE_COMPRESSION = 'false';
  
  // Need to create a new instance or clear cache if the utility caches the env vars
  console.log('  Compression Enabled:', CompressionUtil.isCompressionEnabled());
  console.log('  Should compress large data:', CompressionUtil.shouldCompress(largeData));
  
  const disabledCompressionResult = await CompressionUtil.compressData(largeData);
  console.log('  Compression ratio:', disabledCompressionResult.compressionRatio.toFixed(2) + '%');
  console.log();

  // Test 5: Test different thresholds
  console.log('🎯 Testing Different Thresholds:');
  process.env.ENABLE_COMPRESSION = 'true';
  
  const testThresholds = [1024, 5120, 10240, 51200]; // 1KB, 5KB, 10KB, 50KB
  
  for (const threshold of testThresholds) {
    process.env.COMPRESSION_THRESHOLD = threshold.toString();
    const shouldCompress = CompressionUtil.shouldCompress(largeData);
    console.log(`  Threshold ${CompressionUtil.formatBytes(threshold)}: ${shouldCompress ? 'COMPRESS' : 'NO COMPRESS'}`);
  }
  console.log();

  console.log('✅ Compression configuration test completed!');
}

// Test React Native compression utility if available
async function testReactNativeConfig() {
  try {
    const { CompressionUtil: RNCompressionUtil } = require('./CompressionUtil');
    
    console.log('\n📱 Testing React Native Compression Configuration\n');
    
    // Test default configuration
    console.log('📋 Default Configuration:');
    RNCompressionUtil.logCompressionConfig();
    console.log();
    
    // Test with compression disabled
    console.log('🚫 Testing with Compression Disabled:');
    global.__COMPRESSION_DISABLED__ = true;
    console.log('  Compression Enabled:', RNCompressionUtil.isCompressionEnabled());
    console.log();
    
    // Test with custom threshold
    console.log('🎯 Testing with Custom Threshold:');
    global.__COMPRESSION_DISABLED__ = false;
    global.__COMPRESSION_THRESHOLD__ = 10240; // 10KB
    console.log('  Compression Threshold:', RNCompressionUtil.formatBytes(RNCompressionUtil.getCompressionThreshold()));
    console.log();
    
    // Test compression with small data
    const smallData = { test: 'data' };
    const shouldCompressSmall = RNCompressionUtil.shouldCompress(smallData);
    console.log('📦 Small Data Test:');
    console.log('  Should compress:', shouldCompressSmall);
    
    const compressionResult = RNCompressionUtil.compressData(smallData);
    console.log('  Compressed:', compressionResult.compressed);
    console.log();
    
    console.log('✅ React Native compression configuration test completed!');
    
  } catch (error) {
    console.log('ℹ️  React Native compression utility not found or not testable in Node.js environment');
  }
}

// Run tests
async function runAllTests() {
  try {
    await testCompressionConfig();
    await testReactNativeConfig();
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error(error.stack);
  }
}

// Only run if this file is executed directly
if (require.main === module) {
  runAllTests();
}

module.exports = {
  testCompressionConfig,
  testReactNativeConfig,
  runAllTests
};
