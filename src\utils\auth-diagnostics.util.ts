import { Request } from 'express';
import { logger } from './logger';
import { googleTokenService } from '../services/googleToken.service';
import * as dotenvx from '@dotenvx/dotenvx';

export interface AuthDiagnosticResult {
  success: boolean;
  authMode: 'google' | 'jwt';
  tokenPresent: boolean;
  tokenLength?: number;
  tokenType?: 'google-id' | 'jwt' | 'test' | 'unknown';
  validationResult?: any;
  error?: string;
  recommendations: string[];
}

/**
 * Comprehensive authentication diagnostics utility
 */
export class AuthDiagnostics {

  /**
   * Diagnose authentication issues for a request
   */
  static async diagnoseRequest(req: Request): Promise<AuthDiagnosticResult> {
    const result: AuthDiagnosticResult = {
      success: false,
      authMode: dotenvx.get('USE_JWT_AUTH') === 'true' ? 'jwt' : 'google',
      tokenPresent: false,
      recommendations: []
    };

    try {
      // Check for authorization header
      const authHeader = req.headers['authorization'];
      const token = authHeader && authHeader.split(' ')[1];

      result.tokenPresent = !!token;
      result.tokenLength = token ? token.length : 0;

      if (!token) {
        result.error = 'No authorization token provided';
        result.recommendations.push('Include Authorization header with Bearer token');
        result.recommendations.push('Format: Authorization: Bearer <your-token>');
        return result;
      }

      // Determine token type
      result.tokenType = this.determineTokenType(token);

      // Log token analysis
      logger.debug('Auth diagnostics - Token analysis', {
        tokenLength: result.tokenLength,
        tokenType: result.tokenType,
        authMode: result.authMode,
        tokenPreview: token.substring(0, 20) + '...'
      });

      // Validate based on auth mode
      if (result.authMode === 'google') {
        result.validationResult = await this.validateGoogleToken(token);

        if (!result.validationResult.isValid) {
          result.error = result.validationResult.error || 'Google token validation failed';
          result.recommendations.push(...this.getGoogleTokenRecommendations(result.validationResult));
        } else {
          result.success = true;
        }
      } else {
        // JWT validation would go here
        result.error = 'JWT validation not implemented in diagnostics';
        result.recommendations.push('Switch to Google authentication or implement JWT diagnostics');
      }

      return result;

    } catch (error) {
      result.error = `Diagnostic error: ${error instanceof Error ? error.message : String(error)}`;
      result.recommendations.push('Check server logs for detailed error information');
      logger.error('Auth diagnostics error', { error });
      return result;
    }
  }

  /**
   * Determine the type of token based on its structure
   */
  private static determineTokenType(token: string): 'google-id' | 'jwt' | 'test' | 'unknown' {
    if (!token) return 'unknown';

    // Check for test token
    const testToken = dotenvx.get('TEST_TOKEN') || 'test-token';
    if (token === testToken) {
      return 'test';
    }

    // Google ID tokens typically start with 'eyJ' and have 3 parts separated by dots
    if (token.startsWith('eyJ') && token.split('.').length === 3) {
      try {
        // Try to decode the header to check if it's a Google token
        const header = JSON.parse(atob(token.split('.')[0]));
        if (header.alg === 'RS256' && header.typ === 'JWT') {
          // Could be either Google ID token or JWT, check payload
          const payload = JSON.parse(atob(token.split('.')[1]));
          if (payload.iss && (payload.iss.includes('accounts.google.com') || payload.iss.includes('googleapis.com'))) {
            return 'google-id';
          }
          return 'jwt';
        }
      } catch {
        // If decoding fails, it might still be a valid token but corrupted base64
      }
    }

    return 'unknown';
  }

  /**
   * Validate Google token and provide detailed results
   */
  private static async validateGoogleToken(token: string) {
    try {
      const validation = await googleTokenService.validateToken(token);

      // Add additional diagnostic information
      if (!validation.isValid && validation.error) {
        logger.warn('Google token validation failed', {
          error: validation.error,
          tokenLength: token.length,
          tokenPreview: token.substring(0, 20) + '...'
        });
      }

      return validation;
    } catch (error) {
      logger.error('Google token validation error', { error });
      return {
        isValid: false,
        error: `Validation service error: ${error instanceof Error ? error.message : String(error)}`
      };
    }
  }

  /**
   * Get recommendations for Google token issues
   */
  private static getGoogleTokenRecommendations(validationResult: any): string[] {
    const recommendations: string[] = [];

    if (validationResult.error) {
      switch (validationResult.error) {
        case 'Token expired':
          recommendations.push('Token has expired - refresh the Google ID token');
          recommendations.push('Implement automatic token refresh in your client');
          recommendations.push('Check token expiration before making requests');
          break;

        case 'Invalid token format':
          recommendations.push('Token format is invalid - ensure you are sending a Google ID token');
          recommendations.push('Verify the token is obtained from Google Sign-In');
          recommendations.push('Check that the token is not corrupted during transmission');
          break;

        case 'Token verification failed':
          recommendations.push('Google rejected the token - it may be invalid or tampered with');
          recommendations.push('Ensure the token is a valid Google ID token');
          recommendations.push('Check that the Google Client ID is correctly configured');
          break;

        case 'Token validation service unavailable':
          recommendations.push('Google token validation service is unavailable');
          recommendations.push('Check internet connectivity');
          recommendations.push('Retry the request after a short delay');
          recommendations.push('Consider implementing fallback authentication');
          break;

        default:
          recommendations.push('Unknown token validation error');
          recommendations.push('Check server logs for more details');
          recommendations.push('Verify Google authentication configuration');
      }
    }

    // General recommendations
    recommendations.push('Ensure GOOGLE_CLIENT_ID is properly configured');
    recommendations.push('Verify the token is fresh (not expired)');
    recommendations.push('Check that the client is using the correct Google Sign-In implementation');

    return recommendations;
  }

  /**
   * Get current authentication configuration
   */
  static getAuthConfig() {
    return {
      authMode: dotenvx.get('USE_JWT_AUTH') === 'true' ? 'jwt' : 'google',
      googleClientId: dotenvx.get('GOOGLE_CLIENT_ID'),
      googleTokenInfoUrl: dotenvx.get('GOOGLE_TOKEN_INFO_URL'),
      googleTokenCacheTtl: dotenvx.get('GOOGLE_TOKEN_CACHE_TTL'),
      useGoogleAuth: dotenvx.get('USE_GOOGLE_AUTH'),
      testToken: dotenvx.get('TEST_TOKEN'),
      mode: dotenvx.get('MODE')
    };
  }

  /**
   * Log comprehensive authentication status
   */
  static logAuthStatus() {
    const config = this.getAuthConfig();

    logger.info('Authentication Configuration Status', {
      authMode: config.authMode,
      googleAuthEnabled: config.useGoogleAuth === 'true',
      hasGoogleClientId: !!config.googleClientId && config.googleClientId !== 'your-google-client-id.apps.googleusercontent.com',
      googleTokenInfoUrl: config.googleTokenInfoUrl,
      cacheTtl: config.googleTokenCacheTtl,
      environment: config.mode,
      hasTestToken: !!config.testToken
    });

    // Warn about configuration issues
    if (config.authMode === 'google' && config.googleClientId === 'your-google-client-id.apps.googleusercontent.com') {
      logger.warn('Google Client ID is not properly configured - using placeholder value');
    }

    if (config.authMode === 'google' && config.useGoogleAuth !== 'true') {
      logger.warn('Google authentication mode selected but USE_GOOGLE_AUTH is not set to true');
    }
  }
}

/**
 * Express middleware for authentication diagnostics
 */
export const authDiagnosticsMiddleware = async (req: any, res: any, next: any) => {
  if (dotenvx.get('MODE') === 'development' && req.query.debug === 'auth') {
    try {
      const diagnostics = await AuthDiagnostics.diagnoseRequest(req);

      res.json({
        success: true,
        diagnostics,
        config: AuthDiagnostics.getAuthConfig(),
        timestamp: new Date().toISOString()
      });
      return;
    } catch (error) {
      res.status(500).json({
        success: false,
        error: 'Diagnostics failed',
        message: error instanceof Error ? error.message : String(error)
      });
      return;
    }
  }

  next();
};
