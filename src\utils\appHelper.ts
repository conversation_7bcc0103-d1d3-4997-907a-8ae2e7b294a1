import { logger } from '../utils/logger'

/**
 * Calculates if user answer matches correct answers exactly
 * @param userAnswer String containing user's selected answers (e.g. "A" or "AB")
 * @param correctAnswers Array of correct answer letters (e.g. ["A", "D"])
 * @returns boolean indicating if the answer is correct
 */
export const isCorrectAnswer = (userAnswer: string | string[], correctAnswers: string[]): boolean => {
    // Handle empty/null cases
    if (!userAnswer) return correctAnswers.length === 0;
    
    // Convert user answer string to array of letters and sort
    const userAnswerArray = typeof userAnswer === 'string' ? userAnswer.trim().split('').sort() : userAnswer.sort();
    const sortedCorrectAnswers = [...correctAnswers].sort();
  
    // First check if lengths match
    if (userAnswerArray.length !== sortedCorrectAnswers.length) {
      return false;
    }
  
    const result = userAnswerArray.every((letter, index) => letter.toLowerCase() === sortedCorrectAnswers[index].toLowerCase());
    // Then check if all elements match
    return result;
}

export const parseAiResponse = (answer: string, type = 1) => {
    if (type == 1) {
        const answerMatch = answer.split("**Answers:**")[1].split("**Confidence:**")[0];
        const confidenceMatch = answer.split("**Confidence:**")[1].split("**Explanation:**")[0];
        const explanation1 = answer.split("**Explanation:**")[1].split("**Follow-up Questions:**")[0];
        const followUpQuestions = answer.split("**Follow-up Questions:**")[1];
    
        return {
            answer: answerMatch ? answerMatch.trim() : "",
            confidence: confidenceMatch ? parseInt(confidenceMatch.trim()) || 0 : 0,
            explanation: explanation1 ? explanation1.trim() : "",
            follow_up_questions: followUpQuestions ? followUpQuestions.split('\n').map((q: string) => q.replace(/^\d+\.\s*/, '').trim().replace("- ", "")).filter((q: string) => q.length > 0) : [],
            raw_answer: answer
        }
    } else {
        const answerContent = answer.split("**Follow-up Questions:**")[0]
        const followUpQuestions = answer.split("**Follow-up Questions:**")[1];
        const answerRS = answerContent.replace(/\*\*:/g, "\n").replace(/\*\*/g, "\n");
        const rs =   {
            answer: answerRS,
            follow_up_questions: followUpQuestions ? followUpQuestions.split('\n').map((q: string) => q.replace(/^\d+\.\s*/, '').trim().replace("- ", "")).filter((q: string) => q.length > 0) : [],
            raw_answer: answer
        };
        logger.debug("parseAiResponse: " + JSON.stringify(rs));
        return rs;
    }
}