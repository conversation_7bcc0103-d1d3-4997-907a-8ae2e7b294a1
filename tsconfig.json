{
  "compilerOptions": {
    "experimentalDecorators": true,
    "target": "ESNext",
    "lib": ["ESNext"],
    "module": "commonjs",
    "outDir": "./dist",
    "rootDir": "./src",
    "strict": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "moduleResolution": "node",
    "typeRoots": ["./node_modules/@types", "./src/types"]
  },
  "include": ["src/**/*"],
  /* "exclude": ["node_modules"] */
}
