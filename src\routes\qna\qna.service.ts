import moment from 'moment';
import { apiConfig } from '../../config/apiConfig';
import { getCollection, getCollections, getModel } from '../../config/database';
import { DbOperations } from '../../utils/dbLogger';
import { IQnA } from './qna.model';
import { ObjectId } from 'mongodb';
import { cacheable } from '../../utils/cacheable';
import { logger } from '../../utils/logger';
import fs from 'fs';
import path from 'path';
import * as dotenvx from '@dotenvx/dotenvx';
import { EmailService } from '../../utils/email.service';

// Helper function to check if a value is a MongoDB ObjectId
function isObjectId(value: any): boolean {
    return value && typeof value === 'object' && value.constructor && value.constructor.name === 'ObjectId';
}

// Helper function to normalize ID values for comparison
function normalizeId(id: any): string {
    if (!id) return '';

    // If it's an ObjectId, convert to string
    if (isObjectId(id)) {
        return id.toString();
    }

    // If it's a string that looks like an ObjectId, return as is
    if (typeof id === 'string' && /^[0-9a-fA-F]{24}$/.test(id)) {
        return id;
    }

    // Otherwise, convert to string
    return String(id);
}

// Helper function to try converting a string to ObjectId
function tryToObjectId(id: any): ObjectId | null {
    try {
        if (typeof id === 'string' && /^[0-9a-fA-F]{24}$/.test(id)) {
            return new ObjectId(id as string);
        }
        return null;
    } catch (error) {
        return null;
    }
}

export class QnAService {

    /* @cacheable() */
    static async getUserQnaStatus(
        user_id: string,
        exam_code: string,
        start_date?: string,
        end_date?: string
    ): Promise<any[]> {
        try {
            // Handle date ranges with UTC and proper time boundaries
            const startDate = start_date
                ? moment.utc(start_date).startOf('day')
                //: moment.utc().startOf('month');
                : moment.utc(0); // Beginning of time

            const endDate = end_date
                ? moment.utc(end_date).endOf('day')
                //: moment.utc().endOf('month');
                : moment.utc().add(100, 'years'); // Far future

            console.log('Start Date:', startDate.toDate());
            console.log('End Date:', endDate.toDate());

            const collection = await getModel('user', 'qna_status');

            // Build precise query
            const query = {
                user: parseInt(user_id, 10),
                code: exam_code,
                $or: [
                    {
                        timestamp_incorrect: {
                            $gte: startDate.toDate(),
                            $lte: endDate.toDate()
                        }
                    },
                    {
                        timestamp_bookmarked: {
                            $gte: startDate.toDate(),
                            $lte: endDate.toDate()
                        }
                    },
                    {
                        timestamp_browsed: {
                            $gte: startDate.toDate(),
                            $lte: endDate.toDate()
                        }
                    }
                ]
            };

            const stats = await DbOperations.find(collection, query);

            return stats.map((stat: any) => ({
                _id: stat._id?.toString(),
                code: stat.code,
                qna_id: stat.qna_id,
                user: stat.user,
                timestamp_incorrect: stat.timestamp_incorrect?.getTime?.(),
                timestamp_bookmarked: stat.timestamp_bookmarked?.getTime?.(),
                timestamp_browsed: stat.timestamp_browsed?.getTime?.(),
                date_incorrect: stat.timestamp_incorrect?.toISOString?.(),
                date_bookmarked: stat.timestamp_bookmarked?.toISOString?.(),
                date_browsed: stat.timestamp_browsed?.toISOString?.(),
            }));
        } catch (error) {
            logger.error('QnaService Error in getUserQnaStatus:', error);
            throw error;
        }
    }

    static async updateQnaStatus(
        user_id: string,
        exam_code: string,
        qna_ids: number[],
        statusUpdates: {
            is_bookmarked?: boolean,
            is_browsed?: boolean,
            is_incorrect?: boolean
        }
    ): Promise<number> {
        try {
            const parsedUserId = parseInt(user_id, 10);
            const collection = await getModel('user', 'qna_status');
            const now = new Date();

            // Prepare bulk write operations
            const bulkOps = qna_ids.map(qna_id => {
                const updateDoc: any = {
                    $set: {
                        user: parsedUserId,
                        code: exam_code,
                        qna_id: qna_id
                    }
                };

                // Add timestamp updates
                if (statusUpdates.is_bookmarked) updateDoc.$set.timestamp_bookmarked = now;
                if (statusUpdates.is_browsed) updateDoc.$set.timestamp_browsed = now;
                if (statusUpdates.is_incorrect) updateDoc.$set.timestamp_incorrect = now;

                return {
                    updateOne: {
                        filter: {
                            user: parsedUserId,
                            code: exam_code,
                            qna_id: qna_id
                        },
                        update: updateDoc,
                        upsert: true
                    }
                };
            });

            // Execute bulk operation
            const result = await collection.bulkWrite(bulkOps);
            return result.modifiedCount + result.upsertedCount;
        } catch (error) {
            logger.error('QnaService Error in updateQnaStatus:', error);
            throw error;
        }
    }

    /**
     * Helper method to apply JSON overrides to QnA data
     * @param results The original QnA data
     * @param overrideData The override data from JSON file
     * @param examCode The exam code for logging purposes
     * @returns The modified QnA data with overrides applied
     */
    private static applyOverrides(results: any[], overrideData: any[], examCode: string): any[] {
        if (!overrideData || !Array.isArray(overrideData) || overrideData.length === 0) {
            logger.debug(`No valid override data found for exam code: ${examCode}`);
            return results;
        }

        logger.debug(`Applying overrides for exam code: ${examCode}`);
        logger.debug(`Override data contains ${overrideData.length} items`);

        // Create a deep copy of the results to avoid modifying the original
        const modifiedResults = JSON.parse(JSON.stringify(results));
        let overridesApplied = 0;
        let fieldsOverridden = 0;

        // Log the IDs in the results for debugging
        //logger.debug(`Result items IDs: ${modifiedResults.map((item: any) => item._id).join(', ')}`);

        // Apply overrides for each item in the override data
        for (const override of overrideData) {
            // Validate override structure
            if (!override._id) {
                logger.debug(`Skipping override item without _id field`);
                continue;
            }

            logger.debug(`Processing override for item with _id: ${override._id} (type: ${typeof override._id})`);
            logger.debug(`Override data: ${JSON.stringify(override)}`);


            // Find the matching item in the results by _id
            const resultIndex = modifiedResults.findIndex((item: any) => {
                if (!item._id) return false;

                // First try: Convert both IDs to normalized strings for comparison
                const itemIdStr = normalizeId(item._id);
                const overrideIdStr = normalizeId(override._id);

                //logger.debug(`Comparing IDs: item._id (${typeof item._id}${isObjectId(item._id) ? ', ObjectId' : ''}): ${itemIdStr}, override._id (${typeof override._id}): ${overrideIdStr}`);

                // Direct string comparison
                if (itemIdStr === overrideIdStr) {
                    return true;
                }

                // Second try: Convert string to ObjectId if possible and compare
                const itemObjectId = isObjectId(item._id) ? item._id : tryToObjectId(item._id);
                const overrideObjectId = isObjectId(override._id) ? override._id : tryToObjectId(override._id);

                if (itemObjectId && overrideObjectId) {
                    const objectIdMatch = itemObjectId.equals(overrideObjectId);
                    if (objectIdMatch) {
                        logger.debug(`IDs matched using ObjectId.equals() method`);
                        return true;
                    }
                }

                // Third try: Compare the last part of the ID (in case of different formats)
                if (itemIdStr.endsWith(overrideIdStr) || overrideIdStr.endsWith(itemIdStr)) {
                    logger.debug(`IDs matched using endsWith comparison`);
                    return true;
                }

                return false;
            });

            if (resultIndex === -1) {
                logger.debug(`No matching item found for override with _id: ${override._id}`);
                continue;
            }

            logger.debug(`Applying override for item with _id: ${override._id}`);

            // Log the before state of critical fields
            if (override.exam && override.exam.answer) {
                const originalAnswer = modifiedResults[resultIndex].exam?.answer;
                logger.debug(`Before override - Answer: ${JSON.stringify(originalAnswer)}`);
            }

            // Track which fields are being overridden
            const overriddenFields = this.getOverriddenFields(override);
            logger.debug(`Fields being overridden: ${overriddenFields.join(', ')}`);
            fieldsOverridden += overriddenFields.length;

            // Store the original item for comparison
            const originalItem = { ...modifiedResults[resultIndex] };

            // Apply the override by recursively merging objects
            modifiedResults[resultIndex] = this.deepMerge(modifiedResults[resultIndex], override);
            overridesApplied++;

            // Log the after state of critical fields
            if (override.exam && override.exam.answer) {
                const originalAnswer = originalItem.exam?.answer;
                const newAnswer = modifiedResults[resultIndex].exam?.answer;
                logger.debug(`Before override - Answer: ${JSON.stringify(originalAnswer)}`);
                logger.debug(`After override - Answer: ${JSON.stringify(newAnswer)}`);

                // Verify if the override was actually applied
                const answerChanged = JSON.stringify(originalAnswer) !== JSON.stringify(newAnswer);
                logger.debug(`Answer changed: ${answerChanged}`);

                if (!answerChanged) {
                    logger.warn(`WARNING: Override for item ${override._id} did not change the answer field as expected!`);
                    logger.debug(`Original item: ${JSON.stringify(originalItem)}`);
                    logger.debug(`Modified item: ${JSON.stringify(modifiedResults[resultIndex])}`);
                }
            }

            // Log all changed fields for debugging
            const changedFields = this.getChangedFields(originalItem, modifiedResults[resultIndex]);
            logger.debug(`Changed fields: ${changedFields.join(', ') || 'none'}`);

            if (changedFields.length === 0) {
                logger.warn(`WARNING: No fields were changed for item ${override._id}!`);
            }
        }

        logger.debug(`Override summary for ${examCode}: ${overridesApplied} items modified, ${fieldsOverridden} fields overridden`);
        return modifiedResults;
    }

    /**
     * Helper method to compare two objects and identify which fields have changed
     * @param original The original object
     * @param modified The modified object
     * @param prefix Current path prefix for nested fields
     * @returns Array of field paths that were changed
     */
    private static getChangedFields(original: any, modified: any, prefix: string = ''): string[] {
        if (!original || !modified || typeof original !== 'object' || typeof modified !== 'object') {
            return original !== modified ? [prefix || 'value'] : [];
        }

        let changedFields: string[] = [];

        // Check fields in modified that differ from original
        for (const key in modified) {
            const fieldPath = prefix ? `${prefix}.${key}` : key;

            // Skip if key doesn't exist in original
            if (!(key in original)) {
                changedFields.push(fieldPath);
                continue;
            }

            if (typeof modified[key] === 'object' && modified[key] !== null &&
                typeof original[key] === 'object' && original[key] !== null) {
                // Recursively check nested objects
                const nestedChanges = this.getChangedFields(original[key], modified[key], fieldPath);
                changedFields = changedFields.concat(nestedChanges);
            } else if (JSON.stringify(original[key]) !== JSON.stringify(modified[key])) {
                // Values differ
                changedFields.push(fieldPath);
            }
        }

        return changedFields;
    }

    /**
     * Helper method to ensure proper types in objects
     * Particularly useful for ensuring arrays remain arrays when converting between JSON and JavaScript objects
     * @param data The data to process
     * @returns The data with proper types
     */
    static ensureProperTypes(data: any): any {
        // Handle null or undefined
        if (data === null || data === undefined) {
            return data;
        }

        // Handle arrays
        if (Array.isArray(data)) {
            return data.map(item => QnAService.ensureProperTypes(item));
        }

        // Handle objects
        if (typeof data === 'object') {
            const result: any = {};

            for (const key in data) {
                if (Object.prototype.hasOwnProperty.call(data, key)) {
                    // Check if the property looks like an array that was converted to an object
                    // (has numeric keys starting from 0 and no other keys)
                    if (typeof data[key] === 'object' && data[key] !== null && !Array.isArray(data[key])) {
                        const keys = Object.keys(data[key]);
                        const isNumericSequence = keys.every((k, i) => k === i.toString());

                        if (isNumericSequence && keys.length > 0) {
                            // Convert back to array
                            const array = [];
                            for (let i = 0; i < keys.length; i++) {
                                array.push(data[key][i.toString()]);
                            }
                            result[key] = QnAService.ensureProperTypes(array);
                            logger.debug(`Converted object to array for key "${key}": ${JSON.stringify(array)}`);
                        } else {
                            // Regular object, process recursively
                            result[key] = QnAService.ensureProperTypes(data[key]);
                        }
                    } else {
                        // Other types, process recursively if needed
                        result[key] = QnAService.ensureProperTypes(data[key]);
                    }
                }
            }

            return result;
        }

        // Primitive types
        return data;
    }

    /**
     * Helper method to get a list of fields that would be overridden
     * @param obj The override object
     * @param prefix Current path prefix for nested fields
     * @returns Array of field paths that would be overridden
     */
    private static getOverriddenFields(obj: any, prefix: string = ''): string[] {
        if (!obj || typeof obj !== 'object') return [];

        let fields: string[] = [];

        for (const key in obj) {
            if (key === '_id') continue; // Skip _id field

            const fieldPath = prefix ? `${prefix}.${key}` : key;

            if (typeof obj[key] === 'object' && obj[key] !== null && !Array.isArray(obj[key])) {
                // Recursively get fields from nested objects
                fields = fields.concat(this.getOverriddenFields(obj[key], fieldPath));
            } else {
                // Add leaf fields
                fields.push(fieldPath);
            }
        }

        return fields;
    }

    /**
     * Helper method to recursively merge objects
     * 递归合并对象
     * @param target The target object
     * @param source The source object with override values
     * @returns The merged object
     */
    private static deepMerge(target: any, source: any): any {
        // If source is not an object, return source
        // 如果源不是对象或者为null，直接返回源
        if (typeof source !== 'object' || source === null) {
            return source;
        }

        // If target is not an object, initialize it
        // 如果目标不是对象或者为null，初始化目标对象
        if (typeof target !== 'object' || target === null) {
            return { ...source };
        }

        // Create a new object to avoid modifying the original
        // 创建一个新对象以避免修改原始对象
        const result = { ...target };

        // Merge properties from source into result
        // 合并源对象的属性到结果对象中
        for (const key in source) {
            if (Object.prototype.hasOwnProperty.call(source, key)) {
                //if (typeof source[key] === 'object' && source[key] !== null &&
                if (Array.isArray(source[key])) {
                    // 如果源值是数组，直接赋值
                    result[key] = source[key];
                } else if (typeof source[key] === 'object' && source[key] !== null &&
                    typeof result[key] === 'object' && result[key] !== null) {
                    // Recursively merge nested objects
                    // 递归合并嵌套对象
                    result[key] = this.deepMerge(result[key], source[key]);
                } else {
                    // Directly assign non-object properties
                    result[key] = source[key];
                }
            }
        }

        return result;
    }

    @cacheable()
    static async getQna(exam_code: string, vendor_code?: string, is_free: boolean = false): Promise<any[]> {
        try {
            if (!exam_code) {
                throw new Error('exam_code is required');
            }
            logger.debug('Received exam_code:', exam_code);
            logger.debug('Received vendor_code:', vendor_code);
            logger.debug('Received is_free:', is_free);

            const exam_detail = await DbOperations.findOne(
                await getModel('exam', 'detail'),
                { exam_code: { $regex: '^' + exam_code + '$', $options: 'i' } }
            );

            if (!exam_detail) throw new Error('exam_detail not found');

            const targetVendorCode = vendor_code || exam_detail.vendor_code?.toLowerCase();
            if (!targetVendorCode) throw new Error('Vendor code not found');

            logger.debug('Query:', { type: { "$in": ["mc", "mc2"] }, code: exam_detail.code, "subject": { "$exists": true } });

            // Get the results from the database
            const results = await DbOperations.find(
                await getModel('qna', targetVendorCode),
                { type: { "$in": ["mc", "mc2"] }, code: exam_detail.code, "subject": { "$exists": true } },
                { url: 0, timestamp: 0, vote_distribution: 0, timestamp_updated: 0 },
                { /* skip: 0, limit: 10  */ }
            );

            // Apply JSON overrides
            let resultsWithOverrides = await this.applyJsonOverrides(results, exam_detail.code);

            // If is_free is true, return only 25% of the questions sorted by date_added
            if (is_free) {
                // Sort by date_added in ascending order
                resultsWithOverrides.sort((a: any, b: any) => {
                    const dateA = a.date_added ? new Date(a.date_added).getTime() : 0;
                    const dateB = b.date_added ? new Date(b.date_added).getTime() : 0;
                    return dateA - dateB;
                });

                // Calculate 25% of the total questions
                const freeQuestionCount = Math.ceil(resultsWithOverrides.length * 0.25);

                // Return only the first 25% of questions
                resultsWithOverrides = resultsWithOverrides.slice(0, freeQuestionCount);
            }

            return resultsWithOverrides;
        } catch (error) {
            logger.error('QnaService Error:', error);
            throw error;
        }
    }

    @cacheable()
    static async getQnACollections(): Promise<string[] | null> {
        const collections = await getCollections(apiConfig.databaseName.qna);
        return collections;
    }

    @cacheable()
    static async getQnAByCode(code: string): Promise<IQnA[] | null> {
        try {
            const collection = await getCollection(apiConfig.databaseName.qna, code);
            const qnaData = await collection.find({}).toArray();
            qnaData.sort((a: any, b: any) => moment(a.timestamp).valueOf() - moment(b.timestamp).valueOf());
            qnaData.map((qna: any, index: number) => {
                qnaData[index].id = qna._id.toString();
                qnaData[index].timestamp = moment(qna.timestamp * 1000).format(apiConfig.formatDateTime.defaultDate);
            });
            return qnaData as IQnA[] | null;
        } catch (error) {
            console.error('Error fetching QnA by code:', error);
            throw error;
        }
    }

    @cacheable()
    static async getQnASentence(code: string, id: string): Promise<IQnA | null> {
        try {
            const collection = await getCollection(apiConfig.databaseName.qna, code);
            const qnaData = await collection.findOne({ _id: new ObjectId(id as string) });

            if (!qnaData) {
                logger.debug(`No QnA document found for ID: ${id} in collection: ${code}`);
                return null;
            }

            // Apply JSON overrides
            const documentWithOverrides = await this.applyJsonOverrides(qnaData, code);

            return documentWithOverrides as IQnA | null;
        } catch (error) {
            logger.error(`Error fetching QnA by ID: ${id} in collection: ${code}`, error);
            throw error;
        }
    }

    /**
     * Apply JSON overrides to QnA data
     * @param data The original QnA data (single document or array)
     * @param examCode The exam code to use for finding the override file
     * @returns The data with overrides applied
     */
    static async applyJsonOverrides(data: any, examCode: string): Promise<any> {
        if (!data) {
            logger.debug(`No data to apply overrides to for exam code: ${examCode}`);
            return data;
        }

        // Convert single document to array for consistent processing
        const isArray = Array.isArray(data);
        const dataArray = isArray ? data : [data];

        if (dataArray.length === 0) {
            logger.debug(`Empty data array, no overrides to apply for exam code: ${examCode}`);
            return data;
        }

        logger.debug(`Applying JSON overrides for exam code: ${examCode} to ${dataArray.length} document(s)`);

        try {
            // Check if there's a JSON override file
            const qnaDir = path.join(process.cwd(), 'qna');
            const overrideFilePath = path.join(qnaDir, `${examCode}.json`);

            // Create the qna directory if it doesn't exist
            if (!fs.existsSync(qnaDir)) {
                try {
                    logger.debug(`Creating qna directory at ${qnaDir}`);
                    fs.mkdirSync(qnaDir, { recursive: true });
                } catch (error) {
                    const dirError = error as Error;
                    logger.error(`Error creating qna directory: ${dirError.message}`);
                    // Continue without overrides if directory creation fails
                    return data;
                }
            }

            // Check if the override file exists
            if (!fs.existsSync(overrideFilePath)) {
                logger.debug(`No override file found at ${overrideFilePath}`);
                return data;
            }

            logger.debug(`Found override file: ${overrideFilePath}`);

            // Read the override file
            const fileContent = fs.readFileSync(overrideFilePath, 'utf8');

            if (!fileContent || fileContent.trim() === '') {
                logger.error(`Override file is empty: ${overrideFilePath}`);
                return data;
            }

            // Parse the override file
            let overrideData;
            try {
                overrideData = JSON.parse(fileContent);
            } catch (error) {
                const parseError = error as Error;
                logger.error(`Error parsing JSON in override file: ${parseError.message}`);
                return data;
            }

            // Validate the override data structure
            if (!overrideData) {
                logger.error(`Invalid override data: null or undefined`);
                return data;
            }

            // Ensure overrideData is an array
            const overrideArray = Array.isArray(overrideData) ? overrideData : [overrideData];

            // Validate and prepare each override item
            const validOverrides = overrideArray.filter(item => {
                if (!item || typeof item !== 'object') {
                    logger.error(`Invalid override item: not an object`);
                    return false;
                }

                if (!item._id) {
                    logger.error(`Invalid override item: missing _id field`);
                    return false;
                }

                // Try to convert string _id to ObjectId if it's in the correct format
                if (typeof item._id === 'string' && /^[0-9a-fA-F]{24}$/.test(item._id)) {
                    try {
                        // Store the original string ID for logging
                        const originalId = item._id;

                        // Convert to ObjectId using the correct constructor
                        item._id = new ObjectId(item._id as string);

                        logger.debug(`Converted override item _id from string "${originalId}" to ObjectId`);
                    } catch (error) {
                        // If conversion fails, keep the original string
                        logger.debug(`Failed to convert override item _id to ObjectId: ${error}`);
                    }
                }

                return true;
            });

            if (validOverrides.length === 0) {
                logger.error(`No valid override items found in file`);
                return data;
            }

            logger.debug(`Validated ${validOverrides.length} override items`);

            // Ensure the data is properly structured before applying overrides
            const preparedData = QnAService.ensureProperTypes(dataArray);

            // Apply the overrides to the data
            const modifiedData = QnAService.applyOverrides(
                preparedData,
                validOverrides,
                examCode
            );

            // Final check to ensure all arrays are properly preserved
            const finalData = QnAService.ensureProperTypes(modifiedData);

            // Verify that arrays are preserved in the final results
            for (const item of finalData) {
                if (item.exam && item.exam.answer) {
                    if (!Array.isArray(item.exam.answer)) {
                        logger.warn(`WARNING: Answer is not an array in final result for item ${item._id}: ${JSON.stringify(item.exam.answer)}`);
                    } else {
                        //logger.debug(`Answer is properly preserved as array for item ${item._id}: ${JSON.stringify(item.exam.answer)}`);
                    }
                }
            }

            logger.debug(`Successfully applied overrides from ${overrideFilePath}`);

            // Return in the same format as the input (array or single document)
            return isArray ? finalData : finalData[0];
        } catch (error) {
            const overrideError = error as Error;
            logger.error(`Error applying JSON overrides: ${overrideError.message}`);
            logger.error(`Stack trace:`, overrideError.stack);
            // Continue with original data if there's an error with the override
            return data;
        }
    }
    @cacheable()
    static async getQnaByID(id: string): Promise<IQnA | null> {
        try {
            // Query where url matches questionID
            const qnaDocument = await DbOperations.findOne(
                await getModel('qna', dotenvx.get('QNA_VENDOR')),
                { _id: new ObjectId(id) },
                { url: 0, timestamp: 0, vote_distribution: 0, timestamp_updated: 0 },
            );

            if (!qnaDocument) {
                logger.debug(`No QnA document found for URL: ${id}`);
                return null;
            }

            // Extract the exam code from the document
            const examCode = qnaDocument.code;

            if (!examCode) {
                logger.debug(`No exam code found in QnA document for URL: ${id}`);
                return qnaDocument as IQnA | null;
            }

            logger.debug(`Found QnA document with exam code: ${examCode} for URL: ${id}`);

            // Apply JSON overrides
            const documentWithOverrides = await this.applyJsonOverrides(qnaDocument, examCode);

            return documentWithOverrides as IQnA | null;
        } catch (error) {
            logger.error('Error fetching QnA by URL:', error);
            throw error;
        }
    }

    static async submitFeedback(userId: string, qnaId: string, content: string): Promise<boolean> {
        try {
            if (!userId || !qnaId || !content) {
                throw new Error('Missing required parameters: userId, qnaId, or content');
            }

            logger.debug(`Submitting feedback for QnA ID: ${qnaId} from user: ${userId}`);
            
            await EmailService.sendFeedbackEmail(userId, qnaId, content);
            
            logger.debug(`Successfully sent feedback email for QnA ID: ${qnaId}`);
            return true;
        } catch (error) {
            logger.error('Error submitting feedback:', error);
            throw error;
        }
    }

    @cacheable()
    static async getQnaByURL(url: string): Promise<IQnA | null> {
        try {
            // Query where url matches questionID
            const qnaDocument = await DbOperations.findOne(
                await getModel('qna', 'amazon'),
                { url: url },
                { url: 0, timestamp: 0, vote_distribution: 0, timestamp_updated: 0, date_added: 0 },
            );

            if (!qnaDocument) {
                logger.debug(`No QnA document found for URL: ${url}`);
                return null;
            }

            // Extract the exam code from the document
            const examCode = qnaDocument.code;

            if (!examCode) {
                logger.debug(`No exam code found in QnA document for URL: ${url}`);
                return qnaDocument as IQnA | null;
            }

            logger.debug(`Found QnA document with exam code: ${examCode} for URL: ${url}`);

            // Apply JSON overrides
            const documentWithOverrides = await this.applyJsonOverrides(qnaDocument, examCode);

            return documentWithOverrides as IQnA | null;
        } catch (error) {
            logger.error('Error fetching QnA by URL:', error);
            throw error;
        }
    }
}