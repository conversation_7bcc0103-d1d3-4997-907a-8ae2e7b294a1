import { Request, Response } from 'express';
import { AuthService } from './auth.service';
import { logger } from '../../utils/logger';

export class AuthController {
  private authService: AuthService;

  constructor() {
    this.authService = new AuthService();
  }

  /**
   * Login with Google
   */
  async loginWithGoogle(req: Request, res: Response): Promise<void> {
    const requestId = req.headers['x-request-id'] || 'unknown';
    logger.info(`[${requestId}] Google login request received`);

    try {
      const { idToken } = req.body;

      logger.debug(`[${requestId}] Request body:`, {
        hasIdToken: !!idToken,
        idTokenLength: idToken ? idToken.length : 0,
        idTokenPreview: idToken ? `${idToken.substring(0, 10)}...` : 'none'
      });

      if (!idToken) {
        logger.warn(`[${requestId}] No ID token provided`);
        res.status(400).json({ success: false, message: 'ID token is required' });
        return;
      }

      // Extract device info from request
      const deviceInfo = {
        device: req.headers['user-agent'] || 'Unknown',
        platform: req.headers['x-platform'] || 'Unknown',
        ipAddress: req.ip || req.socket.remoteAddress || 'Unknown',
        // In a real implementation, you might use a geolocation service
        location: req.headers['x-location'] || undefined
      };

      logger.debug(`[${requestId}] Device info:`, deviceInfo);
      logger.info(`[${requestId}] Calling authService.loginWithGoogle`);

      const result = await this.authService.loginWithGoogle(idToken, deviceInfo);

      logger.debug(`[${requestId}] AuthService returned result:`, {
        success: result?.success,
        hasToken: !!result?.token,
        tokenLength: result?.token ? result.token.length : 0,
        tokenPreview: result?.token ? `${result.token.substring(0, 10)}...${result.token.substring(result.token.length - 5)}` : 'none',
        hasUser: !!result?.user,
        userId: result?.user?.id
      });

      logger.info(`[${requestId}] Sending response to client`);
      res.json(result);

      logger.info(`[${requestId}] Response sent successfully`);
    } catch (error) {
      logger.error(`[${requestId}] Error in loginWithGoogle controller:`, error);
      res.status(500).json({ success: false, message: 'Internal server error' });
    }
  }

  /**
   * Logout
   */
  async logout(req: Request, res: Response): Promise<void> {
    try {
      const authHeader = req.headers['authorization'];
      const token = authHeader && authHeader.split(' ')[1];

      if (!token) {
        res.status(400).json({ success: false, message: 'Token is required' });
        return;
      }

      const result = await this.authService.logout(token);
      res.json(result);
    } catch (error) {
      logger.error({ error }, 'Error in logout controller');
      res.status(500).json({ success: false, message: 'Internal server error' });
    }
  }

  /**
   * Validate Google token
   */
  async validateToken(req: Request, res: Response): Promise<void> {
    try {
      const authHeader = req.headers['authorization'];
      const token = authHeader && authHeader.split(' ')[1];

      if (!token) {
        res.status(400).json({ success: false, message: 'Token is required' });
        return;
      }

      const result = await this.authService.validateGoogleToken(token);
      res.json(result);
    } catch (error) {
      logger.error({ error }, 'Error in validateToken controller');
      res.status(500).json({ success: false, message: 'Internal server error' });
    }
  }
}