import { Router } from 'express';
import { createBookmark, deleteBookmark, getBookmarksByCode } from './bookmark.controller';
import { authenticateToken } from '../../middleware/auth.middleware';

const router = Router();

/**
 * @swagger
 * /api/bookmark:
 *   get:
 *     summary: Get bookmarks by code
 *     tags: [Bookmark]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: code
 *         required: true
 *         schema:
 *           type: string
 *         description: The code to fetch bookmarks for
 *     responses:
 *       200:
 *         description: List of bookmarks
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 code:
 *                   type: number
 *                   example: 200
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       user_id:
 *                         type: string
 *                       code:
 *                         type: string
 *                       question_id:
 *                         type: string
 *                       created_at:
 *                         type: string
 *                         format: date-time
 *       401:
 *         description: Unauthorized
 *       400:
 *         description: Bad Request
 */
router.get('/', authenticateToken, getBookmarksByCode);

/**
 * @swagger
 * /api/bookmark:
 *   post:
 *     summary: Create a new bookmark
 *     tags: [Bookmark]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               code:
 *                 type: string
 *                 description: The code for the bookmark
 *               qna_id:
 *                 type: number
 *                 description: The ID of the question/answer to bookmark
 *             required:
 *               - code
 *               - qna_id
 *     responses:
 *       200:
 *         description: Bookmark created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 code:
 *                   type: number
 *                   example: 200
 *                 data:
 *                   type: object
 *                   properties:
 *                     user:
 *                       type: number
 *                     code:
 *                       type: string
 *                     qna_id:
 *                       type: number
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server Error
 */

router.post('/', authenticateToken, createBookmark);

/**
 * @swagger
 * /api/bookmark:
 *   delete:
 *     summary: Remove a bookmark
 *     description: Removes a bookmark for the authenticated user
 *     tags: [Bookmark]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               code:
 *                 type: string
 *                 description: The code for the bookmark
 *               qna_id:
 *                 type: number
 *                 description: The ID of the question/answer to remove bookmark from
 *             required:
 *               - code
 *               - qna_id
 *     responses:
 *       200:
 *         description: Bookmark removed successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 code:
 *                   type: number
 *                   example: 200
 *                 data:
 *                   type: boolean
 *                   example: true
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server Error
 */

router.delete('/', authenticateToken, deleteBookmark);

export default router; 