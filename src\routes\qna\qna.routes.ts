import { Router } from 'express';
import { getQnaCollections, getQna, getQnaByCode, getQnaSentence, getUserQnaStatus, updateQnaStatus, submitFeedback } from './qna.controller';
import { authenticateToken } from '../../middleware/auth.middleware';
import { conditionalAuth } from '../../middleware/auth.conditional.middleware';

const router = Router();

/**
 * @swagger
 * /api/qna/user-status:
 *   get:
 *     summary: Get user's QnA status
 *     tags: [QnA]
 *     parameters:
 *       - in: query
 *         name: user_id
 *         required: true
 *         schema:
 *           type: string
 *         example: "25"
 *       - in: query
 *         name: exam_code
 *         required: true
 *         schema:
 *           type: string
 *         example: "abc"
 *       - in: query
 *         name: start_date
 *         schema:
 *           type: string
 *           format: date
 *           description: Start date (defaults to start of current month)
 *         example: "abc"
 *       - in: query
 *         name: end_date
 *         schema:
 *           type: string
 *           format: date
 *           description: End date (defaults to end of current month)
 *     responses:
 *       200:
 *         description: User's QnA statistics
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 code:
 *                   type: integer
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       _id:
 *                         type: string
 *                       code:
 *                         type: string
 *                       qna_id:
 *                         type: integer
 *                       user:
 *                         type: integer
 *                       timestamp_incorrect:
 *                         type: number
 *                         nullable: true
 *                       timestamp_bookmarked:
 *                         type: number
 *                         nullable: true
 *                       timestamp_browsed:
 *                         type: number
 *                         nullable: true
 *       400:
 *         description: Missing required parameters
 *       500:
 *         description: Server error
 */
router.get('/user-status', getUserQnaStatus);

/**
 * @swagger
 * /api/qna/update-status:
 *   put:
 *     summary: Update QnA status for multiple items
 *     tags: [QnA]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - user_id
 *               - exam_code
 *               - qna_ids
 *             properties:
 *               user_id:
 *                 type: string
 *                 example: "25"
 *               exam_code:
 *                 type: string
 *                 example: "abc"
 *               qna_ids:
 *                 type: array
 *                 items:
 *                   type: integer
 *                   example: 2
 *                 example: [2, 5, 8]
 *               is_bookmarked:
 *                 type: boolean
 *                 example: true
 *               is_browsed:
 *                 type: boolean
 *                 example: false
 *               is_incorrect:
 *                 type: boolean
 *                 example: true
 *           examples:
 *             default:
 *               summary: Example request
 *               value:
 *                 user_id: "25"
 *                 exam_code: "abc"
 *                 qna_ids: [2, 5, 8]
 *                 is_bookmarked: true
 *                 is_browsed: false
 *                 is_incorrect: true
 *     responses:
 *       200:
 *         description: Status updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 data:
 *                   type: object
 *                   properties:
 *                     updated_count:
 *                       type: integer
 *                       example: 3
 *       400:
 *         description: Missing parameters or no status specified
 *       500:
 *         description: Server error
 */
router.put('/update-status', updateQnaStatus);

/**
 * @swagger
 * /api/qna:
 *   get:
 *     summary: Get QnA items for an exam
 *     tags: [QnA]
 *     description: Public API to get QnA items without authentication
 *     parameters:
 *       - in: query
 *         name: exam_code
 *         required: true
 *         schema: { type: string }
 *         example: "CLF-C02"
 *       - in: query
 *         name: vendor_code
 *         schema: { type: string }
 *       - in: query
 *         name: is_free
 *         schema:
 *           type: boolean
 *           default: false
 *         description: When true, returns only 25% of questions sorted by date_added in ascending order
 *     responses:
 *       200:
 *         description: QnA results
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success: { type: boolean }
 *                 code: { type: integer }
 *                 data:
 *                   type: array
 */
router.get('/', authenticateToken/* conditionalAuth */, getQna);

/**
 * @swagger
 * /api/qna/details/{code}/{id}:
 *   get:
 *     summary: Get a specific QnA sentence by code and id
 *     tags: [QnA]
 *     description: Public API to get QnA sentence details without authentication
 *     parameters:
 *       - in: path
 *         name: code
 *         required: true
 *         schema:
 *           type: string
 *         description: The QnA collection code
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: The sentence ID
 *     responses:
 *       200:
 *         description: QnA sentence details
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 code:
 *                   type: number
 *                   example: 200
 *                 data:
 *                   type: object
 *                   properties:
 *                     _id:
 *                       type: string
 *                     question:
 *                       type: string
 *                     answer:
 *                       type: string
 *       404:
 *         description: QnA sentence not found
 *       500:
 *         description: Server error
 */

router.get('/details/:code/:id', getQnaSentence);

/**
 * @swagger
 * /api/qna/{code}:
 *   get:
 *     summary: Get QnA by code
 *     tags: [QnA]
 *     description: Public API to get QnA by code without authentication
 *     parameters:
 *       - in: path
 *         name: code
 *         required: true
 *         schema:
 *           type: string
 *         description: QnA code
 *     responses:
 *       200:
 *         description: QnA details
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 code:
 *                   type: number
 *                   example: 200
 *       404:
 *         description: QnA not found
 *       500:
 *         description: Server error
 */
router.get('/:code', getQnaByCode);



/**
 * @swagger
 * /api/qna/feedback:
 *   post:
 *     summary: Submit feedback about a QnA item
 *     tags: [QnA]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - userId
 *               - qnaId
 *               - content
 *             properties:
 *               userId:
 *                 type: string
 *                 description: ID of the user submitting feedback
 *                 example: "user123"
 *               qnaId:
 *                 type: string
 *                 description: ID of the QnA item being feedbacked
 *                 example: "qna456"
 *               content:
 *                 type: string
 *                 description: Feedback content
 *                 example: "The explanation could be clearer"
 *           examples:
 *             default:
 *               summary: Example feedback
 *               value:
 *                 userId: "user123"
 *                 qnaId: "qna456"
 *                 content: "The explanation could be clearer"
 *     responses:
 *       200:
 *         description: Feedback submitted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 data:
 *                   type: object
 *                   properties:
 *                     message:
 *                       type: string
 *                       example: "Feedback submitted successfully"
 *       400:
 *         description: Missing required parameters
 *       500:
 *         description: Server error
 */
router.post('/feedback', submitFeedback);

export default router;