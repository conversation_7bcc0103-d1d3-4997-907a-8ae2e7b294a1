import swaggerJsdoc from 'swagger-jsdoc';
import * as dotenvx from '@dotenvx/dotenvx';

const options: swaggerJsdoc.Options = {
  definition: {
    openapi: '3.1.0',
    tags: [
      {
        name: 'Authentication',
        description: 'Authentication operations'
      },
      {
        name: 'User',
        description: 'User profile operations'
      },
      {
        name: 'User Progress',
        description: 'User course progress operations'
      },
      {
        name: 'Quiz Results',
        description: 'Quiz results operations'
      },
      {
        name: 'Purchases',
        description: 'Purchase operations'
      },
      {
        name: 'AI Credits',
        description: 'AI credit operations'
      },
      {
        name: 'AI Chat',
        description: 'AI chat operations'
      },
      {
        name: 'Login History',
        description: 'Login history operations'
      },
      {
        name: 'QnA',
        description: 'Question and Answer operations'
      },
      {
        name: 'Exams',
        description: 'Exam operations'
      },
      {
        name: 'Bookmark',
        description: 'Bookmark operations'
      },
      {
        name: 'AI',
        description: 'AI operations'
      },
      {
        name: 'Purchase',
        description: 'Purchase operations'
      }
    ],
    info: {
      title: 'Edump API Documentation',
      version: '1.0.0',
      description: 'API documentation for Edump',
      contact: {
        name: 'Edump',
        email: '<EMAIL>',
      },
    },
    servers: [
      {
        url: dotenvx.get('API_URL') || 'http://localhost:3016',
        description: 'Development server',
      },
    ],
    components: {
      securitySchemes: {
        bearerAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT', // Explicit format
          description: 'JWT Authorization'
        },
      },
      schemas: {
        Response: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            code: { type: 'number' },
            data: { type: 'any' }
          }
        },
        Error: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            code: { type: 'number' },
            error: { type: 'string' }
          }
        }
      }
    },
    security: [{
      bearerAuth: [],
    }],
  },
  apis: ['./src/routes/**/*.ts', './src/routes/**/*.js', './src/models/**/*.ts', './src/models/**/*.js',
    './src/controllers/**/*.ts']
};

export const swaggerSpec = swaggerJsdoc(options);