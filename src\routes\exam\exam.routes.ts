import { Router } from 'express';
import { getExamByCode, getExamHistory, getExamList, getExamResult, getExamResultStatisticQna, getVendorList, searchExam, submitExam } from './exam.controller';
import { authenticateToken } from '../../middleware/auth.middleware';

const router = Router();

/**
 * @swagger
 * /api/exam:
 *   get:
 *     summary: get exam list
 *     tags: [Exams]
 *     description: Public API to get exam list without authentication
 *     parameters:
 *       - in: query
 *         name: vendor_code
 *         schema:
 *           type: string
 *           example: "pmi"
 *           required: false
 *         description: filter by vendor code
 *     responses:
 *       200:
 *         description: exam list data
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Exam'
 *       500:
 *         description: server error
 */
router.get('/', getExamList);

/**
 * @swagger
 * /api/exam/submit:
 *   post:
 *     summary: Submit exam answers
 *     tags: [Exams]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               code:
 *                 type: string
 *                 description: Exam code
 *               user_answers:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     question_id:
 *                       type: string
 *                       description: ID of the question
 *                     answer:
 *                       type: string
 *                       description: User's answer to the question
 *               start_time:
 *                 type: string
 *                 format: date-time
 *                 description: Time when exam was started
 *               end_time:
 *                 type: string
 *                 format: date-time
 *                 description: Time when exam was completed
 *             required:
 *               - code
 *               - user_answers
 *               - start_time
 *               - end_time
 *     responses:
 *       200:
 *         description: Exam submitted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 code:
 *                   type: number
 *                   example: 200
 *                 data:
 *                   type: object
 *                   properties:
 *                     right_answers:
 *                       type: number
 *                     wrong_answers:
 *                       type: number
 *                     unanswers:
 *                       type: number
 *                     total_questions:
 *                       type: number
 *                     timetsamp_started:
 *                       type: string
 *                       format: date-time
 *                     timetsamp_ended:
 *                       type: string
 *                       format: date-time
 *                     score:
 *                       type: string
 *                     passing_score:
 *                       type: number
 *                     user:
 *                       type: number
 *       401:
 *         description: Unauthorized
 *       400:
 *         description: Bad Request
 */

router.post('/submit', authenticateToken, submitExam);


/**
 * @swagger
 * /api/exam/search:
 *   post:
 *     summary: Search for exams
 *     tags: [Exams]
 *     description: Search for exams by keyword
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - keyword
 *             properties:
 *               keyword:
 *                 type: string
 *                 description: Search keyword
 *     responses:
 *       200:
 *         description: Search results
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 code:
 *                   type: number
 *                   example: 200
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                       code:
 *                         type: string
 *                       name:
 *                         type: string
 *       400:
 *         description: Bad Request - Missing keyword
 *       404:
 *         description: No results found
 */

router.post('/search', searchExam);

/**
 * @swagger
 * /api/exam/my/history:
 *   post:
 *     summary: Get exam history for authenticated user
 *     tags: [Exams]
 *     description: Retrieves the exam history for the currently authenticated user
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Exam history retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 code:
 *                   type: number
 *                   example: 200
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       exam_name:
 *                         type: string
 *                         example: "AWS Certified Solutions Architect"
 *                       score:
 *                         type: string
 *                         example: "45 out of 60"
 *                       total_questions:
 *                         type: number
 *                         example: 60
 *                       answers_corrected:
 *                         type: number
 *                         example: 45
 *                       started_time:
 *                         type: string
 *                         example: "2024-01-15T10:30:00Z"
 *                       duration:
 *                         type: number
 *                         example: 3600
 *       400:
 *         description: Bad Request - User ID is required
 *       401:
 *         description: Unauthorized - Invalid or missing token
 *       404:
 *         description: No exam history found
 */

router.post('/my/history', authenticateToken, getExamHistory);


/**
 * @swagger
 * /exam/my/history/{result_id}:
 *   post:
 *     summary: Get detailed exam result by ID
 *     description: Retrieves detailed exam result information for a specific exam attempt
 *     tags: [Exam]
 *     parameters:
 *       - in: path
 *         name: result_id
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the exam result to retrieve
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Exam result retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 code:
 *                   type: number
 *                   example: 200
 *                 data:
 *                   type: object
 *                   properties:
 *                     code:
 *                       type: string
 *                       example: "AWS-SAA"
 *                     right_answers:
 *                       type: number
 *                       example: 45
 *                     wrong_answers:
 *                       type: number
 *                       example: 10
 *                     unanswers:
 *                       type: number
 *                       example: 5
 *                     details_result:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           question:
 *                             type: string
 *                             example: "What is the best storage option for..."
 *                           choices:
 *                             type: array
 *                             items:
 *                               type: object
 *                               properties:
 *                                 code:
 *                                   type: string
 *                                   example: "A"
 *                                 text:
 *                                   type: string
 *                                   example: "Amazon S3"
 *                                 expected:
 *                                   type: boolean
 *                                   example: true
 *                                 user_answer:
 *                                   type: boolean
 *                                   example: false
 *                           is_flagged:
 *                             type: boolean
 *                             example: false
 *       401:
 *         description: Unauthorized - Invalid or missing token
 *       404:
 *         description: Exam result not found
 */

router.post('/my/history/:result_id', authenticateToken, getExamResult);

/**
 * @swagger
 * /api/exam/my/qna/{code}:
 *   get:
 *     summary: Get exam QnA statistics of a user result
 *     tags: [Exam]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: code
 *         required: true
 *         schema:
 *           type: string
 *         description: The exam code
 *     responses:
 *       200:
 *         description: Exam QnA statistics retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 code:
 *                   type: number
 *                   example: 200
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       qna_id:
 *                         type: string
 *                         example: "507f1f77bcf86cd799439011"
 *                       right_answers:
 *                         type: number
 *                         example: 1
 *                       wrong_answers:
 *                         type: number
 *                         example: 0
 *                       unanswers:
 *                         type: number
 *                         example: 0
 *                       total_questions:
 *                         type: number
 *                         example: 1
 *       401:
 *         description: Unauthorized - Invalid or missing token
 *       404:
 *         description: Statistics not found
 *       500:
 *         description: Server error
 */

router.post('/my/qna/:code', authenticateToken, getExamResultStatisticQna);


/**
 * @swagger
 * /api/exam/vendors:
 *   post:
 *     summary: Get list of exam vendors
 *     tags: [Exams]
 *     description: Get list of exam vendors with their associated exams
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               search_keyword:
 *                 type: string
 *                 description: Optional keyword to filter vendors
 *     responses:
 *       200:
 *         description: List of vendors retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 code:
 *                   type: number
 *                   example: 200
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       _id:
 *                         type: string
 *                         example: "507f1f77bcf86cd799439011"
 *                       name:
 *                         type: string
 *                         example: "Microsoft"
 *                       code:
 *                         type: string
 *                         example: "MS"
 *                       exams:
 *                         type: array
 *                         items:
 *                           type: object
 *                           properties:
 *                             code:
 *                               type: string
 *                               example: "AZ-900"
 *                             name:
 *                               type: string
 *                               example: "Azure Fundamentals"
 *       404:
 *         description: No vendors found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 code:
 *                   type: number
 *                   example: 404
 *                 error:
 *                   type: string
 *                   example: "No vendors found"
 */

router.post('/vendors', getVendorList);

export default router;
