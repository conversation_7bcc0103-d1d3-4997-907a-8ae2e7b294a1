import mongoose, { Document, Schema } from 'mongoose';
import * as dotenvx from '@dotenvx/dotenvx';

// Progress Item Interface - for tracking individual question interactions
interface IProgressItem {
  id: string;
  timestamp: Date;
}

// Subject Progress Interface - for tracking progress within a subject
interface ISubjectProgress {
  browsed: IProgressItem[];
  bookmarked: IProgressItem[];
  incorrect: IProgressItem[];
  correct: IProgressItem[];
}

// Exam Progress Interface - for tracking progress within an exam
interface IExamProgress {
  [subject: string]: ISubjectProgress;
}

// User Progress Interface - main progress structure
interface IUserProgress {
  exams: { [examId: string]: IExamProgress };
  lastActivity: Date;
}

// Quiz Result Interface
interface IQuizResult {
  quizId: string;
  examCode: string;
  score: number;
  totalQuestions: number;
  correctAnswers: number;
  timeTaken: number; // in seconds
  completedAt: Date;
  answers: { [questionId: string]: string[] }; // Object with question IDs as keys and arrays of selected options as values
  flaggedQuestions: string[]; // Array of question IDs that were flagged
  userId: string;
  resultId: string;
}

// Purchase Interface
interface IPurchase {
  productId: string;
  transactionId: string;
  platform: 'ios' | 'android' | 'web';
  amount: number;
  currency: string;
  purchasedAt: Date;
  expiresAt: Date;
  status: 'active' | 'expired' | 'refunded';
  receipt: string; // Store receipt data for verification
}

// AI Credit Transaction Interface
interface IAICreditTransaction {
  amount: number; // positive for additions, negative for usage
  reason: string; // 'purchase', 'bonus', 'usage', etc.
  timestamp: Date;
  relatedEntityId?: string; // Optional reference to a purchase or chat session
}

// AI Credits Interface
interface IAICredits {
  totalCredits: number;
  usedCredits: number;
  transactions: IAICreditTransaction[];
}

// AI Chat Message Interface
interface IAIChatMessage {
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  tokensUsed: number; // For tracking credit usage
}

// AI Chat Interface
interface IAIChat {
  chatId: string;
  title: string;
  createdAt: Date;
  updatedAt: Date;
  messages: IAIChatMessage[];
}

// Login History Interface
interface ILoginHistory {
  timestamp: Date;
  device: string;
  platform: string;
  ipAddress: string;
  location?: string; // Optional, based on IP geolocation
}

// User Interface
export interface IUser extends Document {
  googleId: string;
  email: string;
  displayName: string;
  profilePicture: string;
  createdAt: Date;
  updatedAt: Date;
  progress: IUserProgress;
  quizResults: IQuizResult[];
  purchases: IPurchase[];
  aiCredits: IAICredits;
  aiChats: IAIChat[];
  loginHistory: ILoginHistory[];
}

// Schema Definitions
const UserProgressSchema = new Schema<IUserProgress>({
  exams: { type: Schema.Types.Mixed, default: {} },
  lastActivity: { type: Date, default: Date.now }
});

const QuizResultSchema = new Schema<IQuizResult>({
  quizId: { type: String, required: true },
  examCode: { type: String, required: true },
  score: { type: Number, required: true },
  totalQuestions: { type: Number, required: true },
  correctAnswers: { type: Number, required: true },
  timeTaken: { type: Number, required: true },
  completedAt: { type: Date, default: Date.now },
  answers: { type: Map, of: [String], required: true }, // Object with question IDs as keys and arrays of selected options as values
  flaggedQuestions: [{ type: String }], // Array of question IDs that were flagged
  userId: { type: String, required: true },
  resultId: { type: String, required: true }
});

const PurchaseSchema = new Schema<IPurchase>({
  productId: { type: String, required: true },
  transactionId: { type: String, required: true },
  platform: { type: String, enum: ['ios', 'android', 'web'], required: true },
  amount: { type: Number, required: true },
  currency: { type: String, required: true },
  purchasedAt: { type: Date, default: Date.now },
  expiresAt: { type: Date },
  status: { type: String, enum: ['active', 'expired', 'refunded'], default: 'active' },
  receipt: { type: String }
});

// Simplified schemas to allow any data structure without validation

const LoginHistorySchema = new Schema<ILoginHistory>({
  timestamp: { type: Date, default: Date.now },
  device: { type: String, required: true },
  platform: { type: String, required: true },
  ipAddress: { type: String, required: true },
  location: { type: String }
});

// Main User Schema
const UserSchema = new Schema<IUser>({
  googleId: { type: String, required: true, unique: true },
  email: { type: String, required: true, unique: true },
  displayName: { type: String, required: true },
  profilePicture: { type: String },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now },
  progress: { type: UserProgressSchema, default: () => ({}) },
  quizResults: [QuizResultSchema],
  purchases: [PurchaseSchema],
  aiCredits: { type: Schema.Types.Mixed, default: () => ({ totalCredits: 0, usedCredits: 0, transactions: [] }) },
  aiChats: { type: Schema.Types.Mixed, default: () => ([]) },
  loginHistory: [LoginHistorySchema]
});

// Get the collection name from environment variables
const userCollectionName = dotenvx.get('USER_DB_COLLECTION') || 'users';

// Create and export the User model with configurable collection name
export const User = mongoose.model<IUser>('User', UserSchema, userCollectionName);
