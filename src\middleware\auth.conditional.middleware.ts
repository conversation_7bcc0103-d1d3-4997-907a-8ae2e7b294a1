import { Request, Response, NextFunction } from 'express';
import { authenticateToken } from './auth.middleware';

export function conditionalAuth(req: Request, res: Response, next: NextFunction) {
    const isFree = req.query.is_free === 'true';
    if (isFree) {
        // If is_free is true, skip authentication
        return next();
    }
    // Otherwise, apply the authenticateToken middleware
    return authenticateToken(req, res, next);
}
