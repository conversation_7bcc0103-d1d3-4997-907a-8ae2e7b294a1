

import { Request, Response } from 'express';
import { PurchaseService } from './purchase.service';
import { apiConfig } from '../../config/apiConfig';

interface AuthenticatedRequest extends Request {
    user_id?: number;
}

export const createWooOrder = async (req: AuthenticatedRequest, res: Response) => {
    const sessionId = req.body.session_id;  
    const productId = req.body.product_id;
    const code = req.body.code ? req.body.code : "";
    const user_id = req.user_id;
    if (!user_id) {
        res.json({
            success: false,
            code: apiConfig.code.unauthorized,
            error: apiConfig.codeMessage.unauthorized
        });
        return;
    }
    const order = await PurchaseService.createWooOrder(sessionId, productId, code, user_id);
    if (!order) {
        res.json({
            success: false,
            code: apiConfig.code.notFound,
            error: apiConfig.codeMessage.notFound
        });
        return;
    }
    if (order.status == 'completed') {
        res.json({
            success: true,
            code: apiConfig.code.success,
            data: {
                order_id: order.id,
                product: order.line_items[0]
            }
        });
    } else {
        res.json({
            success: false,
            code: apiConfig.code.orderNotCompleted,
            error: apiConfig.codeMessage.orderNotCompleted
        });
    }
}

export const getPurchaseProductList = async (req: Request, res: Response) => {
    const purchases = await PurchaseService.getPurchaseProductList();
    if (!purchases) {
        res.json({
            success: false,
            code: apiConfig.code.notFound,
            error: apiConfig.codeMessage.notFound
        });
        return;
    }
    res.json({
        success: true,
        code: apiConfig.code.success,
        data: purchases
    });
}

export const getCheckoutSession = async (req: Request, res: Response) => {
    console.log("getCheckoutSession: ", req.body);
    const price = req.body.price;
    const name = req.body.name || "Default plan name";
    const exam_code = req.body.exam_code || "";
    const image = req.body.image_url || "https://dummyimage.com/600x400/000/fff&text=no+image";
    const email = req.body.email || "";
    const wooProductId = req.body.product_id || "";
    const checkout_temp_id = req.body.checkout_temp_id || "";
    const session = await PurchaseService.createCheckoutSession(
        price, name, image, exam_code, email, req.headers.origin || '', wooProductId, checkout_temp_id);
    if (!session) {
        res.json({
            success: false,
            code: apiConfig.code.notFound,
            error: apiConfig.codeMessage.notFound
        });
        return;
    }
    res.json({
        success: true,
        code: apiConfig.code.success,
        data: {
            checkout_key: session
        }
    });
}

export const getMyPurchase = async (req: AuthenticatedRequest, res: Response) => {
    const user_id = req.user_id;
    if (!user_id) {
        res.json({ message: 'Unauthorized' });
        return;
    }
    const purchases = await PurchaseService.getMyPurchase(parseInt(user_id + ""));
    if (!purchases) {
        res.json({
            success: false,
            code: apiConfig.code.notFound,
            error: apiConfig.codeMessage.notFound
        });
        return;
    }
    res.json({
        success: true,
        code: apiConfig.code.success,
        data: purchases
    });
}