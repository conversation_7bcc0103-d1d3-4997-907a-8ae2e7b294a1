import axios from "axios";
import { apiConfig } from "../../config/apiConfig";
import { getCollection, getModel } from "../../config/database";
import { cacheable, cacheInvalidator } from '../../utils/cacheable';
import { logger } from '../../utils/logger'

export class AIService {
    // previous_conversations: [{ question: string, answer: string }]
    static async ask(question: string, type: number, previous_conversations: any[]): Promise<any> {
        const messages = [];
        if (type == 2) {
            logger.debug("previous_conversations: " + JSON.stringify(previous_conversations));
            for (let i = 0; i < previous_conversations.length; i++) {
                if (i == 0) {
                    messages.push({
                        role: "system",
                        content: previous_conversations[i].question
                    });
                    messages.push(
                        {
                            role: "user",
                            content: "please suggest your answers only, beginning with **Answers:**. Then specify your confidence level of your answers out of 10, beginning with **Confidence:**. Then explain your choice, beginning with **Explanation:**. Then suggest 3 follow-up questions for users to ask in point form concisely that help user to understand the relevant concept more, beginning with **Follow-up Questions:**"
                        });
                    messages.push({
                        role: "assistant",
                        content: previous_conversations[i].answer
                    });
                } else {
                    messages.push({
                        role: "user",
                        content: previous_conversations[i].question
                    });
                    messages.push({
                        role: "assistant",
                        content: previous_conversations[i].answer
                    });
                }
            }
            messages.push({
                role: "user",
                content: question + " Then suggest 3 follow-up questions for users to ask in point form concisely that help user to understand the relevant concept more, beginning with **Follow-up Questions:**"
            });
        } else {
            messages.push(
                {
                    role: "system",
                    content: question
                }
            );
            messages.push(
                {
                    role: "user",
                    content: "please suggest your answers only, beginning with **Answers:**. Then specify your confidence level of your answers out of 10, beginning with **Confidence:**. Then explain your choice, beginning with **Explanation:**. Then suggest 3 follow-up questions for users to ask in point form concisely that help user to understand the relevant concept more, beginning with **Follow-up Questions:**"
                });

        }
        logger.debug("messages: " + JSON.stringify(messages));
        try {
            const response = await axios.post(
                apiConfig.aiEndpoint,
                {
                    model: "gpt-4.1-mini",
                    messages: messages,
                    temperature: 0.7
                },
                {
                    headers: {
                        'Authorization': `Bearer ${apiConfig.aiToken}`,
                        'Content-Type': 'application/json'
                    }
                }
            );
            return response.data;
        } catch (error) {
            logger.error('Error calling AI API:', error);
            throw new Error('Failed to get AI response');
        }
    }

    static async saveAIChatHistory(userId: number, code: string, qna: string, question: string, answer: string) {
        logger.debug("userId: " + userId);
        logger.debug("code: " + code);
        logger.debug("qna: " + qna);
        logger.debug("question: " + question);
        logger.debug("answer: " + JSON.stringify(answer));
        const collection = await getModel(apiConfig.databaseName.user, apiConfig.collectionName.user.ai_chat);
        const existingChat = await collection.findOne({ user: userId, code: code, qna: qna });
        logger.debug("existingChat: " + JSON.stringify(existingChat));
        if (existingChat) {
            await collection.updateOne(
                { user: userId, code: code, qna: qna },
                {
                    $push: {
                        history: {
                            question: question,
                            response: answer
                        }
                    }
                }
            );
        } else {
            await collection.create({
                user: userId || 0,
                code: code,
                qna: qna,
                history: [{
                    question: question,
                    response: answer
                }]
            });
        }

        await cacheInvalidator.byKey(this.name, 'getAIChats', [userId, code, qna]);

    }

    @cacheable()
    static async getAIChats(userId: number, code: string, qna: string) {
        const collection = await getModel(apiConfig.databaseName.user, apiConfig.collectionName.user.ai_chat);
        const history = await collection.findOne({ user: userId, code: code, qna: qna });
        return history;
    }
}