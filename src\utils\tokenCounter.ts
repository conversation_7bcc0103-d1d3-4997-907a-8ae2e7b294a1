import { logger } from './logger';

/**
 * A simple token counter for OpenAI models
 * This is a rough approximation - for precise counting, a tokenizer like tik<PERSON><PERSON> would be needed
 *
 * Approximation based on:
 * - English text: ~4 chars per token
 * - JSON structure: extra tokens for brackets, quotes, etc.
 */
export const countTokens = (text: string): number => {
    if (!text) return 0;

    // Rough approximation: 4 characters per token for English text
    // This is a simplification - actual tokenization is more complex
    const charCount = text.length;
    return Math.ceil(charCount / 4);
};

/**
 * Count tokens in a message object for OpenAI API
 */
export const countMessageTokens = (message: { role: string; content: string }): number => {
    // Base tokens for message structure (role, content markers)
    const baseTokens = 4;

    // Count tokens in role and content
    const roleTokens = countTokens(message.role);
    const contentTokens = countTokens(message.content);

    return baseTokens + roleTokens + contentTokens;
};

/**
 * Count total tokens in an array of messages for OpenAI API
 */
export const countTotalTokens = (messages: { role: string; content: string }[]): number => {
    if (!messages || messages.length === 0) return 0;

    // Base tokens for the messages array structure
    const baseTokens = 2;

    // Sum tokens for each message
    const messageTokens = messages.reduce(
        (total, message) => total + countMessageTokens(message),
        0
    );

    return baseTokens + messageTokens;
};

/**
 * Truncate messages array to fit within token limit
 * Implements a priority-based truncation approach:
 * 1. System messages (containing exam question details) are always preserved intact
 * 2. The most recent user message is always preserved
 * 3. Older messages are removed sequentially, starting with the oldest
 * 4. A summary message is added if more than 3 messages are removed
 *
 * @param messages Array of messages to truncate
 * @param maxTokens Maximum token limit (default: 4000)
 * @param bufferTokens Buffer to leave for safety (default: 100)
 * @returns Truncated array of messages
 */
export const truncateMessages = (
    messages: { role: string; content: string }[],
    maxTokens: number = 4000,
    bufferTokens: number = 100
): { role: string; content: string }[] => {
    if (!messages || messages.length === 0) return [];

    // Target token count with buffer
    const targetTokens = maxTokens - bufferTokens;

    // If already under limit, return as is
    const totalTokens = countTotalTokens(messages);
    if (totalTokens <= targetTokens) {
        logger.debug(`Messages are within token limit: ${totalTokens} <= ${targetTokens}, no truncation needed`);
        return messages;
    }

    logger.debug(`Messages exceed token limit: ${totalTokens} > ${targetTokens}, truncating...`);

    // Extract system message if present (usually first message)
    const systemMessages = messages.filter(m => m.role === 'system');

    // Extract the most recent user message
    const userMessages = messages.filter(m => m.role === 'user');
    const lastUserMessage = userMessages.length > 0 ? userMessages[userMessages.length - 1] : null;

    // Get all other messages (excluding system messages and the last user message)
    const otherMessages = messages.filter(m =>
        m.role !== 'system' &&
        !(m.role === 'user' && lastUserMessage && m.content === lastUserMessage.content)
    );

    // Calculate tokens for priority messages (system + last user)
    const systemTokens = systemMessages.reduce(
        (total, message) => total + countMessageTokens(message),
        0
    );

    const lastUserTokens = lastUserMessage ? countMessageTokens(lastUserMessage) : 0;
    const priorityTokens = systemTokens + lastUserTokens;

    // If priority messages alone exceed the limit, we need to handle this special case
    if (priorityTokens > targetTokens) {
        logger.debug(`Priority messages alone exceed token limit: ${priorityTokens} > ${targetTokens}`);

        // System messages must be preserved intact as per requirements
        // If they alone exceed the limit, we can only include them
        if (systemTokens > targetTokens) {
            logger.debug(`System messages alone exceed token limit: ${systemTokens} > ${targetTokens}`);
            logger.debug(`Preserving only system messages, removing all other messages`);
            return [...systemMessages];
        }

        // If system + last user message exceed the limit, keep system and truncate last user message
        const availableForUser = targetTokens - systemTokens;
        logger.debug(`Truncating last user message to fit within ${availableForUser} tokens`);

        // Create a truncated version of the last user message
        const truncatedUserMessage = {
            role: lastUserMessage!.role,
            content: lastUserMessage!.content
        };

        // Truncate the content if needed
        const userContentTokens = countTokens(truncatedUserMessage.content);
        if (userContentTokens > availableForUser - 4) { // 4 tokens for message structure
            const charsToKeep = Math.floor((availableForUser - 4) * 4); // Approximate chars per token
            truncatedUserMessage.content = truncatedUserMessage.content.substring(0, charsToKeep) + '...';
        }

        return [...systemMessages, truncatedUserMessage];
    }

    // At this point, we know we can keep all system messages and the last user message
    // Now we need to fit as many other messages as possible

    // Start with priority messages
    const result = [...systemMessages];
    if (lastUserMessage) {
        result.push(lastUserMessage);
    }

    let currentTokens = priorityTokens;
    let removedMessages = 0;

    // Sort other messages chronologically (oldest first)
    const sortedOtherMessages = [...otherMessages].sort((a: any, b: any) => {
        // If we have timestamps, use them (assuming they're stored in a timestamp property)
        if (a.timestamp && b.timestamp) {
            return parseInt(a.timestamp) - parseInt(b.timestamp);
        }
        // Otherwise, we can't determine order, so preserve original order
        return 0;
    });

    // Log the messages we're considering
    logger.debug(`Priority messages: ${systemMessages.length} system, ${lastUserMessage ? 1 : 0} last user`);
    logger.debug(`Other messages to consider: ${sortedOtherMessages.length}`);

    // Try to add messages from newest to oldest
    const messagesToAdd = [];
    for (let i = sortedOtherMessages.length - 1; i >= 0; i--) {
        const message = sortedOtherMessages[i];
        const messageTokens = countMessageTokens(message);

        // Check if adding this message would exceed our target
        if (currentTokens + messageTokens <= targetTokens) {
            messagesToAdd.unshift(message); // Add to beginning to maintain chronological order
            currentTokens += messageTokens;
            logger.debug(`Including message: ${message.role} (${messageTokens} tokens)`);
        } else {
            removedMessages++;
            logger.debug(`Removing message: ${message.role} (${messageTokens} tokens)`);
        }
    }

    // Insert messages in chronological order (after system, before last user)
    if (lastUserMessage) {
        // If we have a last user message, insert other messages before it
        const lastUserIndex = result.indexOf(lastUserMessage);
        result.splice(lastUserIndex, 0, ...messagesToAdd);
    } else {
        // Otherwise, just append them
        result.push(...messagesToAdd);
    }

    // Add a summary message if we removed more than 3 messages
    if (removedMessages > 3) {
        const summaryMessage = {
            role: 'system',
            content: `[${removedMessages} previous messages have been omitted due to length constraints]`
        };

        // Calculate tokens for summary message
        const summaryTokens = countMessageTokens(summaryMessage);

        // Only add if it fits within our target
        if (currentTokens + summaryTokens <= targetTokens) {
            // Insert after system messages but before other messages
            const insertIndex = systemMessages.length;
            result.splice(insertIndex, 0, summaryMessage);
            currentTokens += summaryTokens;
            logger.debug(`Added summary message: "${summaryMessage.content}" (${summaryTokens} tokens)`);
        } else {
            logger.debug(`Summary message would exceed token limit, skipping`);
        }
    }

    // Final log of what we're returning
    logger.debug(`Truncated messages from ${messages.length} to ${result.length}`);
    logger.debug(`Final token count: ${currentTokens}/${targetTokens} (${totalTokens - currentTokens} tokens removed)`);
    logger.debug(`Messages preserved: ${result.length}, Messages removed: ${removedMessages}`);

    return result;
};
