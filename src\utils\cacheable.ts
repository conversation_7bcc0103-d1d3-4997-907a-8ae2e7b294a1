import { Cacheable } from 'cacheable';
import { logger } from './logger';

// Get the current environment mode from dotenvx
const NODE_ENV = process.env.NODE_ENV || 'development';
const isDevelopmentMode = NODE_ENV === 'development';

// Log the current environment mode
logger.info(`Current environment mode: ${NODE_ENV}`);
if (isDevelopmentMode) {
    logger.info('Running in development mode - caching will be disabled');
}

/**
 * Utility function to check if caching is enabled
 * @returns boolean indicating if caching is enabled
 */
export const isCachingEnabled = (): boolean => {
    return !isDevelopmentMode;
};

// Initialize the cache
export const cache = new Cacheable();

type CacheKeyParams = Array<string | number | boolean>;

export function cacheable<T extends any[], R>(
    options: {
        ttl?: number; // TTL in milliseconds (default: 1 hour)
        keyBuilder?: (...args: T) => CacheKeyParams;
    } = {}
) {
    return function (
        target: any,
        methodName: string,
        descriptor: TypedPropertyDescriptor<(...args: T) => Promise<R>>
    ) {
        const originalMethod = descriptor.value!;
        const className = target.name;

        // Default TTL: 1 hour in milliseconds
        const defaultTTL = 60 * 60 * 1000; // 1 hour in ms
        //const defaultTTL = 5 * 1000; // 5s in ms for testing
        const ttl = options.ttl ?? defaultTTL;

        descriptor.value = async function (...args: T): Promise<R> {
            // Auto-generate cache key: ClassName:MethodName:params
            const params = options.keyBuilder?.(...args) || args;
            const cacheKey = `${className}:${methodName}:${JSON.stringify(params)}`;

            // If in development mode, bypass cache completely
            if (isDevelopmentMode) {
                logger.debug(`[Cache disabled] Development mode - bypassing cache for ${cacheKey}`);
                return await originalMethod.apply(this, args);
            }

            logger.info(`Checking cache for key: ${cacheKey}`);

            // Try cache first
            const cached = await cache.get<R>(cacheKey);
            if (cached) {
                logger.info(`[Cache hit] ${cacheKey}`);
                return cached;
            }

            // Cache miss - execute original method
            logger.info(`[Cache miss] ${cacheKey}`);
            const result = await originalMethod.apply(this, args);

            // Set cache with TTL
            await cache.set(cacheKey, result, ttl);
            //await cache.set(cacheKey, result);

            logger.info(`[Cache updated] ${cacheKey}`);

            return result;
        };

        return descriptor;
    };
}


// 2. Cache Invalidation Utility
export const cacheInvalidator = {
    /**
     * Invalidate a specific cache entry.
     * @param className - The class name where the method is defined.
     * @param methodName - The method name being cached.
     * @param params - The parameters used to generate the cache key.
     */
    async byKey(className: string, methodName: string, params: CacheKeyParams): Promise<void> {
      // In development mode, caching is disabled so invalidation is not needed
      if (isDevelopmentMode) {
        logger.debug(`[Cache invalidate skipped] Development mode - cache is disabled`);
        return;
      }

      const cacheKey = `${className}:${methodName}:${JSON.stringify(params)}`;
      logger.info(`[Cache invalidate] ${cacheKey}`);
      await cache.delete(cacheKey);
    },

    /**
     * Clear the entire cache.
     * This can be useful during testing or when major data changes occur.
     */
    async clearAll(): Promise<void> {
      if (isDevelopmentMode) {
        logger.debug(`[Cache clear skipped] Development mode - cache is disabled`);
        return;
      }

      logger.info(`[Cache clear] Clearing entire cache`);
      await cache.clear();
    }
  };