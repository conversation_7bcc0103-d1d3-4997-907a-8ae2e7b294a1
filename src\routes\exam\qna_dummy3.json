[{"_id": {"$oid": "679e05c962fb0d0bbe484801"}, "url": "/discussions/dummy/view/20001-exam-dummy-exam-1-question-1-discussion/", "code": "dummy-exam-1", "subject": 2, "timestamp": {"$date": "2025-02-01T11:00:00.000Z"}, "date_added": "2024-03-01", "first_sentence": "Lorem ipsum dolor sit amet, consectetur adipiscing elit?", "type": "mc", "exam": {"question": "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris?", "answer": ["B"], "choices": [{"A": "Sed ut perspiciatis unde omnis iste natus error"}, {"B": "At vero eos et accusamus et iusto odio dignissimos (correct answer)"}, {"C": "Temporibus autem quibusdam et aut officiis"}, {"D": "Nam libero tempore, cum soluta nobis"}]}, "vote_distribution": {}}, {"_id": {"$oid": "679e05c962fb0d0bbe484802"}, "url": "/discussions/dummy/view/20002-exam-dummy-exam-1-question-2-discussion/", "code": "dummy-exam-1", "subject": 1, "timestamp": {"$date": "2025-02-01T11:01:00.000Z"}, "date_added": "2024-03-02", "first_sentence": "Sed ut perspiciatis unde omnis iste natus error sit voluptatem?", "type": "mc", "exam": {"question": "Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium, totam rem aperiam, eaque ipsa quae ab illo inventore veritatis?", "answer": ["A", "C"], "choices": [{"A": "Nemo enim ipsam voluptatem quia voluptas sit (correct answer)"}, {"B": "Neque porro quisquam est, qui dolorem ipsum"}, {"C": "Ut enim ad minima veniam, quis nostrum (correct answer)"}, {"D": "Quis autem vel eum iure reprehenderit"}]}, "vote_distribution": {}}, {"_id": {"$oid": "679e05c962fb0d0bbe484803"}, "url": "/discussions/dummy/view/20003-exam-dummy-exam-1-question-3-discussion/", "code": "dummy-exam-1", "subject": 3, "timestamp": {"$date": "2025-02-01T11:02:00.000Z"}, "date_added": "2024-03-03", "first_sentence": "At vero eos et accusamus et iusto odio dignissimos?", "type": "mc", "exam": {"question": "At vero eos et accusamus et iusto odio dignissimos ducimus qui blanditiis praesentium voluptatum deleniti atque corrupti quos dolores?", "answer": ["D"], "choices": [{"A": "Et harum quidem rerum facilis est et expedita"}, {"B": "Nam libero tempore, cum soluta nobis est"}, {"C": "Temporibus autem quibusdam et aut officiis"}, {"D": "Itaque earum rerum hic tenetur a sapiente delectus (correct answer)"}]}, "vote_distribution": {}}, {"_id": {"$oid": "679e05c962fb0d0bbe484804"}, "url": "/discussions/dummy/view/20004-exam-dummy-exam-1-question-4-discussion/", "code": "dummy-exam-1", "subject": 1, "timestamp": {"$date": "2025-02-01T11:03:00.000Z"}, "date_added": "2024-03-04", "first_sentence": "Temporibus autem quibusdam et aut officiis debitis?", "type": "mc", "exam": {"question": "Temporibus autem quibusdam et aut officiis debitis aut rerum necessitatibus saepe eveniet ut et voluptates repudiandae sint et molestiae non recusandae?", "answer": ["A"], "choices": [{"A": "Itaque earum rerum hic tenetur a sapiente (correct answer)"}, {"B": "Sed ut perspiciatis unde omnis iste natus"}, {"C": "Lorem ipsum dolor sit amet consectetur"}, {"D": "Nemo enim ipsam voluptatem quia voluptas"}]}, "vote_distribution": {}}, {"_id": {"$oid": "679e05c962fb0d0bbe484805"}, "url": "/discussions/dummy/view/20005-exam-dummy-exam-1-question-5-discussion/", "code": "dummy-exam-1", "subject": 2, "timestamp": {"$date": "2025-02-01T11:04:00.000Z"}, "date_added": "2024-03-05", "first_sentence": "Nam libero tempore, cum soluta nobis est eligendi?", "type": "mc", "exam": {"question": "Nam libero tempore, cum soluta nobis est eligendi optio cumque nihil impedit quo minus id quod maxime placeat facere possimus?", "answer": ["B", "D"], "choices": [{"A": "Omnis voluptas assumenda est, omnis dolor"}, {"B": "Omnis dolor repellendus temporibus autem (correct answer)"}, {"C": "Quibusdam et aut officiis debitis aut rerum"}, {"D": "Necessitatibus saepe eveniet ut et voluptates (correct answer)"}]}, "vote_distribution": {}}, {"_id": {"$oid": "679e05c962fb0d0bbe484806"}, "url": "/discussions/dummy/view/20006-exam-dummy-exam-1-question-6-discussion/", "code": "dummy-exam-1", "subject": 3, "timestamp": {"$date": "2025-02-01T11:05:00.000Z"}, "date_added": "2024-03-06", "first_sentence": "Et harum quidem rerum facilis est et expedita distinctio?", "type": "mc", "exam": {"question": "Et harum quidem rerum facilis est et expedita distinctio. Nam libero tempore, cum soluta nobis est eligendi optio cumque nihil impedit quo minus?", "answer": ["C"], "choices": [{"A": "Sed ut perspiciatis unde omnis iste natus"}, {"B": "Lorem ipsum dolor sit amet consectetur"}, {"C": "Id quod maxime placeat facere possimus (correct answer)"}, {"D": "Omnis voluptas assumenda est omnis dolor"}]}, "vote_distribution": {}}, {"_id": {"$oid": "679e05c962fb0d0bbe484807"}, "url": "/discussions/dummy/view/20007-exam-dummy-exam-1-question-7-discussion/", "code": "dummy-exam-1", "subject": 1, "timestamp": {"$date": "2025-02-01T11:06:00.000Z"}, "date_added": "2024-03-07", "first_sentence": "Omnis voluptas assumenda est, omnis dolor repellendus?", "type": "mc", "exam": {"question": "Omnis voluptas assumenda est, omnis dolor repellendus. Temporibus autem quibusdam et aut officiis debitis aut rerum necessitatibus saepe eveniet?", "answer": ["A", "B"], "choices": [{"A": "Ut et voluptates repudiandae sint et molestiae (correct answer)"}, {"B": "Non recusandae itaque earum rerum hic tenetur (correct answer)"}, {"C": "Sapiente delectus ut aut reiciendis voluptatibus"}, {"D": "<PERSON><PERSON> alias consequatur aut perferendis doloribus"}]}, "vote_distribution": {}}, {"_id": {"$oid": "679e05c962fb0d0bbe484808"}, "url": "/discussions/dummy/view/20008-exam-dummy-exam-1-question-8-discussion/", "code": "dummy-exam-1", "subject": 2, "timestamp": {"$date": "2025-02-01T11:07:00.000Z"}, "date_added": "2024-03-08", "first_sentence": "Ut enim ad minima veniam, quis nostrum exercitationem?", "type": "mc", "exam": {"question": "Ut enim ad minima veniam, quis nostrum exercitationem ullam corporis suscipit laboriosam, nisi ut aliquid ex ea commodi consequatur?", "answer": ["D"], "choices": [{"A": "Quis autem vel eum iure reprehenderit qui"}, {"B": "In ea voluptate velit esse quam nihil"}, {"C": "Molestiae consequatur vel illum qui dolorem"}, {"D": "Eum fugiat quo voluptas nulla pariatur (correct answer)"}]}, "vote_distribution": {}}, {"_id": {"$oid": "679e05c962fb0d0bbe484809"}, "url": "/discussions/dummy/view/20009-exam-dummy-exam-1-question-9-discussion/", "code": "dummy-exam-1", "subject": 3, "timestamp": {"$date": "2025-02-01T11:08:00.000Z"}, "date_added": "2024-03-09", "first_sentence": "Quis autem vel eum iure reprehenderit qui in ea?", "type": "mc", "exam": {"question": "Quis autem vel eum iure reprehenderit qui in ea voluptate velit esse quam nihil molestiae consequatur, vel illum qui dolorem eum fugiat?", "answer": ["B"], "choices": [{"A": "Quo voluptas nulla pariatur at vero eos"}, {"B": "Et accusamus et iusto odio dignissimos ducimus (correct answer)"}, {"C": "Qui blanditiis praesentium voluptatum deleniti"}, {"D": "Atque corrupti quos dolores et quas molestias"}]}, "vote_distribution": {}}, {"_id": {"$oid": "679e05c962fb0d0bbe484810"}, "url": "/discussions/dummy/view/20010-exam-dummy-exam-1-question-10-discussion/", "code": "dummy-exam-1", "subject": 1, "timestamp": {"$date": "2025-02-01T11:09:00.000Z"}, "date_added": "2024-03-10", "first_sentence": "Excepturi sint occaecati cupiditate non provident?", "type": "mc", "exam": {"question": "Excepturi sint occaecati cupiditate non provident, similique sunt in culpa qui officia deserunt mollitia animi, id est laborum et dolorum fuga?", "answer": ["A", "C"], "choices": [{"A": "Et harum quidem rerum facilis est et expedita (correct answer)"}, {"B": "Distinctio nam libero tempore cum soluta nobis"}, {"C": "Est eligendi optio cumque nihil impedit quo (correct answer)"}, {"D": "Minus id quod maxime placeat facere possimus"}]}, "vote_distribution": {}}, {"_id": {"$oid": "679e05c962fb0d0bbe484811"}, "url": "/discussions/dummy/view/20011-exam-dummy-exam-1-question-11-discussion/", "code": "dummy-exam-1", "subject": 2, "timestamp": {"$date": "2025-02-01T11:10:00.000Z"}, "date_added": "2024-03-11", "first_sentence": "Omnis iste natus error sit voluptatem accusantium?", "type": "mc", "exam": {"question": "Omnis iste natus error sit voluptatem accusantium doloremque laudantium, totam rem aperiam, eaque ipsa quae ab illo inventore veritatis et quasi?", "answer": ["C"], "choices": [{"A": "Architecto beatae vitae dicta sunt explicabo"}, {"B": "Nemo enim ipsam voluptatem quia voluptas"}, {"C": "Sit aspernatur aut odit aut fugit sed quia (correct answer)"}, {"D": "Consequuntur magni dolores eos qui ratione"}]}, "vote_distribution": {}}, {"_id": {"$oid": "679e05c962fb0d0bbe484812"}, "url": "/discussions/dummy/view/20012-exam-dummy-exam-1-question-12-discussion/", "code": "dummy-exam-1", "subject": 3, "timestamp": {"$date": "2025-02-01T11:11:00.000Z"}, "date_added": "2024-03-12", "first_sentence": "Voluptatem sequi nesciunt neque porro quisquam est?", "type": "mc", "exam": {"question": "Voluptatem sequi nesciunt neque porro quisquam est, qui dolorem ipsum quia dolor sit amet, consectetur, adipisci velit, sed quia non numquam?", "answer": ["A", "D"], "choices": [{"A": "Eius modi tempora incidunt ut labore et dolore (correct answer)"}, {"B": "Magnam aliquam quaerat voluptatem ut enim"}, {"C": "Ad minima veniam quis nostrum exercitationem"}, {"D": "Ullam corporis suscipit laboriosam nisi ut (correct answer)"}]}, "vote_distribution": {}}, {"_id": {"$oid": "679e05c962fb0d0bbe484813"}, "url": "/discussions/dummy/view/20013-exam-dummy-exam-1-question-13-discussion/", "code": "dummy-exam-1", "subject": 1, "timestamp": {"$date": "2025-02-01T11:12:00.000Z"}, "date_added": "2024-03-13", "first_sentence": "Aliquid ex ea commodi consequatur quis autem?", "type": "mc", "exam": {"question": "Aliquid ex ea commodi consequatur quis autem vel eum iure reprehenderit qui in ea voluptate velit esse quam nihil molestiae consequatur?", "answer": ["B"], "choices": [{"A": "Vel illum qui dolorem eum fugiat quo"}, {"B": "Voluptas nulla pariatur excepturi sint occaecati (correct answer)"}, {"C": "Cupiditate non provident similique sunt in"}, {"D": "Culpa qui officia deserunt mollitia animi"}]}, "vote_distribution": {}}, {"_id": {"$oid": "679e05c962fb0d0bbe484814"}, "url": "/discussions/dummy/view/20014-exam-dummy-exam-1-question-14-discussion/", "code": "dummy-exam-1", "subject": 2, "timestamp": {"$date": "2025-02-01T11:13:00.000Z"}, "date_added": "2024-03-14", "first_sentence": "Id est laborum et dolorum fuga et harum?", "type": "mc", "exam": {"question": "Id est laborum et dolorum fuga et harum quidem rerum facilis est et expedita distinctio nam libero tempore, cum soluta nobis est eligendi?", "answer": ["A"], "choices": [{"A": "Optio cumque nihil impedit quo minus id quod (correct answer)"}, {"B": "Maxime placeat facere possimus omnis voluptas"}, {"C": "Assumenda est omnis dolor repellendus temporibus"}, {"D": "Autem quibusdam et aut officiis debitis aut"}]}, "vote_distribution": {}}, {"_id": {"$oid": "679e05c962fb0d0bbe484815"}, "url": "/discussions/dummy/view/20015-exam-dummy-exam-1-question-15-discussion/", "code": "dummy-exam-1", "subject": 3, "timestamp": {"$date": "2025-02-01T11:14:00.000Z"}, "date_added": "2024-03-15", "first_sentence": "Rerum necessitatibus saepe eveniet ut et voluptates?", "type": "mc", "exam": {"question": "Rerum necessitatibus saepe eveniet ut et voluptates repudiandae sint et molestiae non recusandae itaque earum rerum hic tenetur a sapiente?", "answer": ["B", "C"], "choices": [{"A": "Delectus ut aut reiciendis voluptatibus maiores"}, {"B": "<PERSON><PERSON> consequatur aut perferendis doloribus asperiores (correct answer)"}, {"C": "Repellat sed ut perspiciatis unde omnis iste (correct answer)"}, {"D": "Natus error sit voluptatem accusantium doloremque"}]}, "vote_distribution": {}}, {"_id": {"$oid": "679e05c962fb0d0bbe484816"}, "url": "/discussions/dummy/view/20016-exam-dummy-exam-1-question-16-discussion/", "code": "dummy-exam-1", "subject": 1, "timestamp": {"$date": "2025-02-01T11:15:00.000Z"}, "date_added": "2024-03-16", "first_sentence": "Laudantium totam rem aperiam eaque ipsa quae?", "type": "mc", "exam": {"question": "Laudantium totam rem aperiam eaque ipsa quae ab illo inventore veritatis et quasi architecto beatae vitae dicta sunt explicabo?", "answer": ["D"], "choices": [{"A": "Nemo enim ipsam voluptatem quia voluptas sit"}, {"B": "Aspernatur aut odit aut fugit sed quia"}, {"C": "Consequuntur magni dolores eos qui ratione"}, {"D": "Voluptatem sequi nesciunt neque porro quisquam (correct answer)"}]}, "vote_distribution": {}}, {"_id": {"$oid": "679e05c962fb0d0bbe484817"}, "url": "/discussions/dummy/view/20017-exam-dummy-exam-1-question-17-discussion/", "code": "dummy-exam-1", "subject": 2, "timestamp": {"$date": "2025-02-01T11:16:00.000Z"}, "date_added": "2024-03-17", "first_sentence": "Est qui dolorem ipsum quia dolor sit amet?", "type": "mc", "exam": {"question": "Est qui dolorem ipsum quia dolor sit amet, consectetur, adipisci velit, sed quia non numquam eius modi tempora incidunt ut labore et dolore?", "answer": ["A"], "choices": [{"A": "Magnam aliquam quaerat voluptatem ut enim ad (correct answer)"}, {"B": "Minima veniam quis nostrum exercitationem ullam"}, {"C": "Corporis suscipit laboriosam nisi ut aliquid"}, {"D": "Ex ea commodi consequatur quis autem vel"}]}, "vote_distribution": {}}, {"_id": {"$oid": "679e05c962fb0d0bbe484818"}, "url": "/discussions/dummy/view/20018-exam-dummy-exam-2-question-1-discussion/", "code": "dummy-exam-2", "subject": 3, "timestamp": {"$date": "2025-02-01T11:17:00.000Z"}, "date_added": "2024-03-18", "first_sentence": "Consectetur adipiscing elit sed do eiusmod tempor?", "type": "mc", "exam": {"question": "Consectetur adipiscing elit sed do eiusmod tempor incididunt ut labore et dolore magna aliqua ut enim ad minim veniam quis nostrud exercitation?", "answer": ["C"], "choices": [{"A": "Ullamco laboris nisi ut aliquip ex ea commodo"}, {"B": "Consequat duis aute irure dolor in reprehenderit"}, {"C": "In voluptate velit esse cillum dolore eu fugiat (correct answer)"}, {"D": "<PERSON><PERSON><PERSON> pariatur excepteur sint occaecat cupidatat"}]}, "vote_distribution": {}}, {"_id": {"$oid": "679e05c962fb0d0bbe484819"}, "url": "/discussions/dummy/view/20019-exam-dummy-exam-2-question-2-discussion/", "code": "dummy-exam-2", "subject": 1, "timestamp": {"$date": "2025-02-01T11:18:00.000Z"}, "date_added": "2024-03-19", "first_sentence": "Non proident sunt in culpa qui officia deserunt?", "type": "mc", "exam": {"question": "Non proident sunt in culpa qui officia deserunt mollitia animi id est laborum et dolorum fuga et harum quidem rerum facilis est?", "answer": ["A", "B"], "choices": [{"A": "Et expedita distinctio nam libero tempore cum (correct answer)"}, {"B": "Soluta nobis est eligendi optio cumque nihil (correct answer)"}, {"C": "Impedit quo minus id quod maxime placeat"}, {"D": "Facere possimus omnis voluptas assumenda est"}]}, "vote_distribution": {}}, {"_id": {"$oid": "679e05c962fb0d0bbe484820"}, "url": "/discussions/dummy/view/20020-exam-dummy-exam-2-question-3-discussion/", "code": "dummy-exam-2", "subject": 2, "timestamp": {"$date": "2025-02-01T11:19:00.000Z"}, "date_added": "2024-03-20", "first_sentence": "Omnis dolor repellendus temporibus autem quibusdam?", "type": "mc", "exam": {"question": "Omnis dolor repellendus temporibus autem quibusdam et aut officiis debitis aut rerum necessitatibus saepe eveniet ut et voluptates repudiandae?", "answer": ["D"], "choices": [{"A": "Sint et molestiae non recusandae itaque earum"}, {"B": "Rerum hic tenetur a sapiente delectus ut"}, {"C": "Aut reiciendis voluptatibus maiores alias consequatur"}, {"D": "Aut perferendis doloribus asperiores repellat (correct answer)"}]}, "vote_distribution": {}}, {"_id": {"$oid": "679e05c962fb0d0bbe484821"}, "url": "/discussions/dummy/view/20021-exam-dummy-exam-2-question-4-discussion/", "code": "dummy-exam-2", "subject": 3, "timestamp": {"$date": "2025-02-01T11:20:00.000Z"}, "date_added": "2024-03-21", "first_sentence": "Sed ut perspiciatis unde omnis iste natus error?", "type": "mc", "exam": {"question": "Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium totam rem aperiam eaque ipsa quae ab illo?", "answer": ["B"], "choices": [{"A": "Inventore veritatis et quasi architecto beatae"}, {"B": "Vitae dicta sunt explicabo nemo enim ipsam (correct answer)"}, {"C": "Voluptatem quia voluptas sit aspernatur aut odit"}, {"D": "Aut fugit sed quia consequuntur magni dolores"}]}, "vote_distribution": {}}, {"_id": {"$oid": "679e05c962fb0d0bbe484822"}, "url": "/discussions/dummy/view/20022-exam-dummy-exam-2-question-5-discussion/", "code": "dummy-exam-2", "subject": 1, "timestamp": {"$date": "2025-02-01T11:21:00.000Z"}, "date_added": "2024-03-22", "first_sentence": "Eos qui ratione voluptatem sequi nesciunt neque?", "type": "mc", "exam": {"question": "Eos qui ratione voluptatem sequi nesciunt neque porro quisquam est qui dolorem ipsum quia dolor sit amet consectetur adipisci velit?", "answer": ["A", "C"], "choices": [{"A": "Sed quia non numquam eius modi tempora (correct answer)"}, {"B": "Incidunt ut labore et dolore magnam aliquam"}, {"C": "Quaerat voluptatem ut enim ad minima veniam (correct answer)"}, {"D": "Quis nostrum exercitationem ullam corporis suscipit"}]}, "vote_distribution": {}}, {"_id": {"$oid": "679e05c962fb0d0bbe484823"}, "url": "/discussions/dummy/view/20023-exam-dummy-exam-2-question-6-discussion/", "code": "dummy-exam-2", "subject": 2, "timestamp": {"$date": "2025-02-01T11:22:00.000Z"}, "date_added": "2024-03-23", "first_sentence": "Laboriosam nisi ut aliquid ex ea commodi consequatur?", "type": "mc", "exam": {"question": "Laboriosam nisi ut aliquid ex ea commodi consequatur quis autem vel eum iure reprehenderit qui in ea voluptate velit esse quam nihil molestiae?", "answer": ["D"], "choices": [{"A": "Consequatur vel illum qui dolorem eum fugiat"}, {"B": "Quo voluptas nulla pariatur excepturi sint"}, {"C": "Occaecati cupiditate non provident similique sunt"}, {"D": "In culpa qui officia deserunt mollitia animi (correct answer)"}]}, "vote_distribution": {}}, {"_id": {"$oid": "679e05c962fb0d0bbe484824"}, "url": "/discussions/dummy/view/20024-exam-dummy-exam-2-question-7-discussion/", "code": "dummy-exam-2", "subject": 3, "timestamp": {"$date": "2025-02-01T11:23:00.000Z"}, "date_added": "2024-03-24", "first_sentence": "Id est laborum et dolorum fuga et harum quidem?", "type": "mc", "exam": {"question": "Id est laborum et dolorum fuga et harum quidem rerum facilis est et expedita distinctio nam libero tempore cum soluta nobis est eligendi?", "answer": ["A", "B"], "choices": [{"A": "Optio cumque nihil impedit quo minus id (correct answer)"}, {"B": "Quod maxime placeat facere possimus omnis (correct answer)"}, {"C": "Voluptas assumenda est omnis dolor repellendus"}, {"D": "Temporibus autem quibusdam et aut officiis"}]}, "vote_distribution": {}}, {"_id": {"$oid": "679e05c962fb0d0bbe484825"}, "url": "/discussions/dummy/view/20025-exam-dummy-exam-2-question-8-discussion/", "code": "dummy-exam-2", "subject": 1, "timestamp": {"$date": "2025-02-01T11:24:00.000Z"}, "date_added": "2024-03-25", "first_sentence": "Debitis aut rerum necessitatibus saepe eveniet ut?", "type": "mc", "exam": {"question": "Debitis aut rerum necessitatibus saepe eveniet ut et voluptates repudiandae sint et molestiae non recusandae itaque earum rerum hic tenetur?", "answer": ["C"], "choices": [{"A": "A sapiente delectus ut aut reiciendis voluptatibus"}, {"B": "<PERSON><PERSON> alias consequatur aut perferendis doloribus"}, {"C": "Asperiores repellat sed ut perspiciatis unde (correct answer)"}, {"D": "Omnis iste natus error sit voluptatem accusantium"}]}, "vote_distribution": {}}, {"_id": {"$oid": "679e05c962fb0d0bbe484826"}, "url": "/discussions/dummy/view/20026-exam-dummy-exam-2-question-9-discussion/", "code": "dummy-exam-2", "subject": 2, "timestamp": {"$date": "2025-02-01T11:25:00.000Z"}, "date_added": "2024-03-26", "first_sentence": "Doloremque laudantium totam rem aperiam eaque ipsa?", "type": "mc", "exam": {"question": "Doloremque laudantium totam rem aperiam eaque ipsa quae ab illo inventore veritatis et quasi architecto beatae vitae dicta sunt explicabo?", "answer": ["B"], "choices": [{"A": "Nemo enim ipsam voluptatem quia voluptas sit"}, {"B": "Aspernatur aut odit aut fugit sed quia (correct answer)"}, {"C": "Consequuntur magni dolores eos qui ratione"}, {"D": "Voluptatem sequi nesciunt neque porro quisquam"}]}, "vote_distribution": {}}, {"_id": {"$oid": "679e05c962fb0d0bbe484827"}, "url": "/discussions/dummy/view/20027-exam-dummy-exam-2-question-10-discussion/", "code": "dummy-exam-2", "subject": 3, "timestamp": {"$date": "2025-02-01T11:26:00.000Z"}, "date_added": "2024-03-27", "first_sentence": "Est qui dolorem ipsum quia dolor sit amet consectetur?", "type": "mc", "exam": {"question": "Est qui dolorem ipsum quia dolor sit amet consectetur adipisci velit sed quia non numquam eius modi tempora incidunt ut labore et dolore?", "answer": ["A", "D"], "choices": [{"A": "Magnam aliquam quaerat voluptatem ut enim (correct answer)"}, {"B": "Ad minima veniam quis nostrum exercitationem"}, {"C": "Ullam corporis suscipit laboriosam nisi ut"}, {"D": "Aliquid ex ea commodi consequatur quis autem (correct answer)"}]}, "vote_distribution": {}}, {"_id": {"$oid": "679e05c962fb0d0bbe484828"}, "url": "/discussions/dummy/view/20028-exam-dummy-exam-2-question-11-discussion/", "code": "dummy-exam-2", "subject": 1, "timestamp": {"$date": "2025-02-01T11:27:00.000Z"}, "date_added": "2024-03-28", "first_sentence": "Vel eum iure reprehenderit qui in ea voluptate?", "type": "mc", "exam": {"question": "Vel eum iure reprehenderit qui in ea voluptate velit esse quam nihil molestiae consequatur vel illum qui dolorem eum fugiat quo voluptas nulla?", "answer": ["C"], "choices": [{"A": "Pariatur excepturi sint occaecati cupiditate non"}, {"B": "Provident similique sunt in culpa qui officia"}, {"C": "Deserunt mollitia animi id est laborum et (correct answer)"}, {"D": "Dolorum fuga et harum quidem rerum facilis"}]}, "vote_distribution": {}}, {"_id": {"$oid": "679e05c962fb0d0bbe484829"}, "url": "/discussions/dummy/view/20029-exam-dummy-exam-2-question-12-discussion/", "code": "dummy-exam-2", "subject": 2, "timestamp": {"$date": "2025-02-01T11:28:00.000Z"}, "date_added": "2024-03-29", "first_sentence": "Est et expedita distinctio nam libero tempore?", "type": "mc", "exam": {"question": "Est et expedita distinctio nam libero tempore cum soluta nobis est eligendi optio cumque nihil impedit quo minus id quod maxime placeat?", "answer": ["A", "B"], "choices": [{"A": "<PERSON>re possimus omnis voluptas assumenda est (correct answer)"}, {"B": "Omnis dolor repellendus temporibus autem quibusdam (correct answer)"}, {"C": "Et aut officiis debitis aut rerum necessitatibus"}, {"D": "<PERSON>epe eveniet ut et voluptates repudiandae sint"}]}, "vote_distribution": {}}, {"_id": {"$oid": "679e05c962fb0d0bbe484830"}, "url": "/discussions/dummy/view/20030-exam-dummy-exam-2-question-13-discussion/", "code": "dummy-exam-2", "subject": 3, "timestamp": {"$date": "2025-02-01T11:29:00.000Z"}, "date_added": "2024-03-30", "first_sentence": "Et molestiae non recusandae itaque earum rerum?", "type": "mc", "exam": {"question": "Et molestiae non recusandae itaque earum rerum hic tenetur a sapiente delectus ut aut reiciendis voluptatibus maiores alias consequatur?", "answer": ["D"], "choices": [{"A": "Aut perferendis doloribus asperiores repellat sed"}, {"B": "Ut perspiciatis unde omnis iste natus error"}, {"C": "Sit voluptatem accusantium doloremque laudantium"}, {"D": "Totam rem aperiam eaque ipsa quae ab illo (correct answer)"}]}, "vote_distribution": {}}, {"_id": {"$oid": "679e05c962fb0d0bbe484831"}, "url": "/discussions/dummy/view/20031-exam-dummy-exam-2-question-14-discussion/", "code": "dummy-exam-2", "subject": 1, "timestamp": {"$date": "2025-02-01T11:30:00.000Z"}, "date_added": "2024-03-31", "first_sentence": "Inventore veritatis et quasi architecto beatae vitae?", "type": "mc", "exam": {"question": "Inventore veritatis et quasi architecto beatae vitae dicta sunt explicabo nemo enim ipsam voluptatem quia voluptas sit aspernatur aut odit?", "answer": ["B"], "choices": [{"A": "Aut fugit sed quia consequuntur magni dolores"}, {"B": "Eos qui ratione voluptatem sequi nesciunt (correct answer)"}, {"C": "Neque porro quisquam est qui dolorem ipsum"}, {"D": "Quia dolor sit amet consectetur adipisci velit"}]}, "vote_distribution": {}}, {"_id": {"$oid": "679e05c962fb0d0bbe484832"}, "url": "/discussions/dummy/view/20032-exam-dummy-exam-2-question-15-discussion/", "code": "dummy-exam-2", "subject": 2, "timestamp": {"$date": "2025-02-01T11:31:00.000Z"}, "date_added": "2024-04-01", "first_sentence": "Sed quia non numquam eius modi tempora incidunt?", "type": "mc", "exam": {"question": "Sed quia non numquam eius modi tempora incidunt ut labore et dolore magnam aliquam quaerat voluptatem ut enim ad minima veniam?", "answer": ["A", "C"], "choices": [{"A": "Quis nostrum exercitationem ullam corporis (correct answer)"}, {"B": "Suscipit laboriosam nisi ut aliquid ex ea"}, {"C": "Commodi consequatur quis autem vel eum iure (correct answer)"}, {"D": "Reprehenderit qui in ea voluptate velit esse"}]}, "vote_distribution": {}}, {"_id": {"$oid": "679e05c962fb0d0bbe484833"}, "url": "/discussions/dummy/view/20033-exam-dummy-exam-2-question-16-discussion/", "code": "dummy-exam-2", "subject": 3, "timestamp": {"$date": "2025-02-01T11:32:00.000Z"}, "date_added": "2024-04-02", "first_sentence": "Quam nihil molestiae consequatur vel illum qui?", "type": "mc", "exam": {"question": "Quam nihil molestiae consequatur vel illum qui dolorem eum fugiat quo voluptas nulla pariatur excepturi sint occaecati cupiditate non provident?", "answer": ["A"], "choices": [{"A": "<PERSON><PERSON><PERSON> sunt in culpa qui officia deserunt (correct answer)"}, {"B": "Mollitia animi id est laborum et dolorum"}, {"C": "Fuga et harum quidem rerum facilis est"}, {"D": "Et expedita distinctio nam libero tempore"}]}, "vote_distribution": {}}, {"_id": {"$oid": "679e05c962fb0d0bbe484834"}, "url": "/discussions/dummy/view/20034-exam-dummy-exam-2-question-17-discussion/", "code": "dummy-exam-2", "subject": 1, "timestamp": {"$date": "2025-02-01T11:33:00.000Z"}, "date_added": "2024-04-03", "first_sentence": "Cum soluta nobis est eligendi optio cumque nihil?", "type": "mc", "exam": {"question": "Cum soluta nobis est eligendi optio cumque nihil impedit quo minus id quod maxime placeat facere possimus omnis voluptas assumenda est?", "answer": ["D"], "choices": [{"A": "Omnis dolor repellendus temporibus autem quibusdam"}, {"B": "Et aut officiis debitis aut rerum necessitatibus"}, {"C": "<PERSON><PERSON><PERSON> eveniet ut et voluptates repudiandae"}, {"D": "Sint et molestiae non recusandae itaque earum (correct answer)"}]}, "vote_distribution": {}}, {"_id": {"$oid": "679e05c962fb0d0bbe484835"}, "url": "/discussions/dummy/view/20035-exam-dummy-exam-3-question-1-discussion/", "code": "dummy-exam-3", "subject": 2, "timestamp": {"$date": "2025-02-01T11:34:00.000Z"}, "date_added": "2024-04-04", "first_sentence": "Rerum hic tenetur a sapiente delectus ut aut?", "type": "mc", "exam": {"question": "Rerum hic tenetur a sapiente delectus ut aut reiciendis voluptatibus maiores alias consequatur aut perferendis doloribus asperiores repellat?", "answer": ["B"], "choices": [{"A": "Sed ut perspiciatis unde omnis iste natus"}, {"B": "Error sit voluptatem accusantium doloremque (correct answer)"}, {"C": "Laudantium totam rem aperiam eaque ipsa"}, {"D": "Quae ab illo inventore veritatis et quasi"}]}, "vote_distribution": {}}, {"_id": {"$oid": "679e05c962fb0d0bbe484836"}, "url": "/discussions/dummy/view/20036-exam-dummy-exam-3-question-2-discussion/", "code": "dummy-exam-3", "subject": 3, "timestamp": {"$date": "2025-02-01T11:35:00.000Z"}, "date_added": "2024-04-05", "first_sentence": "Architecto beatae vitae dicta sunt explicabo nemo?", "type": "mc", "exam": {"question": "Architecto beatae vitae dicta sunt explicabo nemo enim ipsam voluptatem quia voluptas sit aspernatur aut odit aut fugit sed quia?", "answer": ["A", "C"], "choices": [{"A": "Consequuntur magni dolores eos qui ratione (correct answer)"}, {"B": "Voluptatem sequi nesciunt neque porro quisquam"}, {"C": "Est qui dolorem ipsum quia dolor sit amet (correct answer)"}, {"D": "Consectetur adipisci velit sed quia non numquam"}]}, "vote_distribution": {}}, {"_id": {"$oid": "679e05c962fb0d0bbe484837"}, "url": "/discussions/dummy/view/20037-exam-dummy-exam-3-question-3-discussion/", "code": "dummy-exam-3", "subject": 1, "timestamp": {"$date": "2025-02-01T11:36:00.000Z"}, "date_added": "2024-04-06", "first_sentence": "Eius modi tempora incidunt ut labore et dolore?", "type": "mc", "exam": {"question": "Eius modi tempora incidunt ut labore et dolore magnam aliquam quaerat voluptatem ut enim ad minima veniam quis nostrum exercitationem?", "answer": ["D"], "choices": [{"A": "Ullam corporis suscipit laboriosam nisi ut"}, {"B": "Aliquid ex ea commodi consequatur quis autem"}, {"C": "Vel eum iure reprehenderit qui in ea"}, {"D": "Voluptate velit esse quam nihil molestiae (correct answer)"}]}, "vote_distribution": {}}, {"_id": {"$oid": "679e05c962fb0d0bbe484838"}, "url": "/discussions/dummy/view/20038-exam-dummy-exam-3-question-4-discussion/", "code": "dummy-exam-3", "subject": 2, "timestamp": {"$date": "2025-02-01T11:37:00.000Z"}, "date_added": "2024-04-07", "first_sentence": "Consequatur vel illum qui dolorem eum fugiat?", "type": "mc", "exam": {"question": "Consequatur vel illum qui dolorem eum fugiat quo voluptas nulla pariatur excepturi sint occaecati cupiditate non provident similique sunt?", "answer": ["C"], "choices": [{"A": "In culpa qui officia deserunt mollitia animi"}, {"B": "Id est laborum et dolorum fuga et harum"}, {"C": "Quidem rerum facilis est et expedita distinctio (correct answer)"}, {"D": "Nam libero tempore cum soluta nobis est"}]}, "vote_distribution": {}}, {"_id": {"$oid": "679e05c962fb0d0bbe484839"}, "url": "/discussions/dummy/view/20039-exam-dummy-exam-3-question-5-discussion/", "code": "dummy-exam-3", "subject": 3, "timestamp": {"$date": "2025-02-01T11:38:00.000Z"}, "date_added": "2024-04-08", "first_sentence": "Eligendi optio cumque nihil impedit quo minus?", "type": "mc", "exam": {"question": "Eligendi optio cumque nihil impedit quo minus id quod maxime placeat facere possimus omnis voluptas assumenda est omnis dolor repellendus?", "answer": ["A", "B"], "choices": [{"A": "Temporibus autem quibusdam et aut officiis (correct answer)"}, {"B": "Debitis aut rerum necessitatibus saepe eveniet (correct answer)"}, {"C": "Ut et voluptates repudiandae sint et molestiae"}, {"D": "Non recusandae itaque earum rerum hic tenetur"}]}, "vote_distribution": {}}, {"_id": {"$oid": "679e05c962fb0d0bbe484840"}, "url": "/discussions/dummy/view/20040-exam-dummy-exam-3-question-6-discussion/", "code": "dummy-exam-3", "subject": 1, "timestamp": {"$date": "2025-02-01T11:39:00.000Z"}, "date_added": "2024-04-09", "first_sentence": "A sapiente delectus ut aut reiciendis voluptatibus?", "type": "mc", "exam": {"question": "A sapiente delectus ut aut reiciendis voluptatibus maiores alias consequatur aut perferendis doloribus asperiores repellat sed ut perspiciatis unde?", "answer": ["D"], "choices": [{"A": "Omnis iste natus error sit voluptatem accusantium"}, {"B": "Doloremque laudantium totam rem aperiam eaque"}, {"C": "Ipsa quae ab illo inventore veritatis et"}, {"D": "Quasi architecto beatae vitae dicta sunt (correct answer)"}]}, "vote_distribution": {}}, {"_id": {"$oid": "679e05c962fb0d0bbe484841"}, "url": "/discussions/dummy/view/20041-exam-dummy-exam-3-question-7-discussion/", "code": "dummy-exam-3", "subject": 2, "timestamp": {"$date": "2025-02-01T11:40:00.000Z"}, "date_added": "2024-04-10", "first_sentence": "Explicabo nemo enim ipsam voluptatem quia voluptas?", "type": "mc", "exam": {"question": "Explicabo nemo enim ipsam voluptatem quia voluptas sit aspernatur aut odit aut fugit sed quia consequuntur magni dolores eos qui ratione?", "answer": ["A", "C"], "choices": [{"A": "Voluptatem sequi nesciunt neque porro quisquam (correct answer)"}, {"B": "Est qui dolorem ipsum quia dolor sit"}, {"C": "Amet consectetur adipisci velit sed quia non (correct answer)"}, {"D": "Numquam eius modi tempora incidunt ut labore"}]}, "vote_distribution": {}}, {"_id": {"$oid": "679e05c962fb0d0bbe484842"}, "url": "/discussions/dummy/view/20042-exam-dummy-exam-3-question-8-discussion/", "code": "dummy-exam-3", "subject": 3, "timestamp": {"$date": "2025-02-01T11:41:00.000Z"}, "date_added": "2024-04-11", "first_sentence": "Et dolore magnam aliquam quaerat voluptatem ut?", "type": "mc", "exam": {"question": "Et dolore magnam aliquam quaerat voluptatem ut enim ad minima veniam quis nostrum exercitationem ullam corporis suscipit laboriosam?", "answer": ["B"], "choices": [{"A": "Nisi ut aliquid ex ea commodi consequatur"}, {"B": "Quis autem vel eum iure reprehenderit qui (correct answer)"}, {"C": "In ea voluptate velit esse quam nihil"}, {"D": "Molestiae consequatur vel illum qui dolorem"}]}, "vote_distribution": {}}, {"_id": {"$oid": "679e05c962fb0d0bbe484843"}, "url": "/discussions/dummy/view/20043-exam-dummy-exam-3-question-9-discussion/", "code": "dummy-exam-3", "subject": 1, "timestamp": {"$date": "2025-02-01T11:42:00.000Z"}, "date_added": "2024-04-12", "first_sentence": "Eum fugiat quo voluptas nulla pariatur excepturi?", "type": "mc", "exam": {"question": "Eum fugiat quo voluptas nulla pariatur excepturi sint occaecati cupiditate non provident similique sunt in culpa qui officia deserunt?", "answer": ["C"], "choices": [{"A": "Mollitia animi id est laborum et dolorum"}, {"B": "Fuga et harum quidem rerum facilis est"}, {"C": "Et expedita distinctio nam libero tempore (correct answer)"}, {"D": "Cum soluta nobis est eligendi optio cumque"}]}, "vote_distribution": {}}, {"_id": {"$oid": "679e05c962fb0d0bbe484844"}, "url": "/discussions/dummy/view/20044-exam-dummy-exam-3-question-10-discussion/", "code": "dummy-exam-3", "subject": 2, "timestamp": {"$date": "2025-02-01T11:43:00.000Z"}, "date_added": "2024-04-13", "first_sentence": "Nihil impedit quo minus id quod maxime placeat?", "type": "mc", "exam": {"question": "Nihil impedit quo minus id quod maxime placeat facere possimus omnis voluptas assumenda est omnis dolor repellendus temporibus autem?", "answer": ["A", "D"], "choices": [{"A": "Quibusdam et aut officiis debitis aut rerum (correct answer)"}, {"B": "Necessitatibus saepe eveniet ut et voluptates"}, {"C": "Repudiandae sint et molestiae non recusandae"}, {"D": "Itaque earum rerum hic tenetur a sapiente (correct answer)"}]}, "vote_distribution": {}}, {"_id": {"$oid": "679e05c962fb0d0bbe484845"}, "url": "/discussions/dummy/view/20045-exam-dummy-exam-3-question-11-discussion/", "code": "dummy-exam-3", "subject": 3, "timestamp": {"$date": "2025-02-01T11:44:00.000Z"}, "date_added": "2024-04-14", "first_sentence": "Delectus ut aut reiciendis voluptatibus maiores alias?", "type": "mc", "exam": {"question": "Delectus ut aut reiciendis voluptatibus maiores alias consequatur aut perferendis doloribus asperiores repellat sed ut perspiciatis unde omnis iste?", "answer": ["B"], "choices": [{"A": "Natus error sit voluptatem accusantium doloremque"}, {"B": "Laudantium totam rem aperiam eaque ipsa quae (correct answer)"}, {"C": "Ab illo inventore veritatis et quasi architecto"}, {"D": "Beatae vitae dicta sunt explicabo nemo enim"}]}, "vote_distribution": {}}, {"_id": {"$oid": "679e05c962fb0d0bbe484846"}, "url": "/discussions/dummy/view/20046-exam-dummy-exam-3-question-12-discussion/", "code": "dummy-exam-3", "subject": 1, "timestamp": {"$date": "2025-02-01T11:45:00.000Z"}, "date_added": "2024-04-15", "first_sentence": "Ipsam voluptatem quia voluptas sit aspernatur aut?", "type": "mc", "exam": {"question": "Ipsam voluptatem quia voluptas sit aspernatur aut odit aut fugit sed quia consequuntur magni dolores eos qui ratione voluptatem sequi?", "answer": ["A", "C"], "choices": [{"A": "Nesciunt neque porro quisquam est qui dolorem (correct answer)"}, {"B": "Ipsum quia dolor sit amet consectetur adipisci"}, {"C": "<PERSON><PERSON>t sed quia non numquam eius modi (correct answer)"}, {"D": "Tempora incidunt ut labore et dolore magnam"}]}, "vote_distribution": {}}, {"_id": {"$oid": "679e05c962fb0d0bbe484847"}, "url": "/discussions/dummy/view/20047-exam-dummy-exam-3-question-13-discussion/", "code": "dummy-exam-3", "subject": 2, "timestamp": {"$date": "2025-02-01T11:46:00.000Z"}, "date_added": "2024-04-16", "first_sentence": "Aliquam quaerat voluptatem ut enim ad minima veniam?", "type": "mc", "exam": {"question": "Aliquam quaerat voluptatem ut enim ad minima veniam quis nostrum exercitationem ullam corporis suscipit laboriosam nisi ut aliquid ex ea?", "answer": ["D"], "choices": [{"A": "Commodi consequatur quis autem vel eum iure"}, {"B": "Reprehenderit qui in ea voluptate velit esse"}, {"C": "Quam nihil molestiae consequatur vel illum qui"}, {"D": "Dolorem eum fugiat quo voluptas nulla pariatur (correct answer)"}]}, "vote_distribution": {}}, {"_id": {"$oid": "679e05c962fb0d0bbe484848"}, "url": "/discussions/dummy/view/20048-exam-dummy-exam-3-question-14-discussion/", "code": "dummy-exam-3", "subject": 3, "timestamp": {"$date": "2025-02-01T11:47:00.000Z"}, "date_added": "2024-04-17", "first_sentence": "Excepturi sint occaecati cupiditate non provident?", "type": "mc", "exam": {"question": "Excepturi sint occaecati cupiditate non provident similique sunt in culpa qui officia deserunt mollitia animi id est laborum et dolorum?", "answer": ["C"], "choices": [{"A": "Fuga et harum quidem rerum facilis est"}, {"B": "Et expedita distinctio nam libero tempore cum"}, {"C": "Soluta nobis est eligendi optio cumque nihil (correct answer)"}, {"D": "Impedit quo minus id quod maxime placeat"}]}, "vote_distribution": {}}, {"_id": {"$oid": "679e05c962fb0d0bbe484849"}, "url": "/discussions/dummy/view/20049-exam-dummy-exam-3-question-15-discussion/", "code": "dummy-exam-3", "subject": 1, "timestamp": {"$date": "2025-02-01T11:48:00.000Z"}, "date_added": "2024-04-18", "first_sentence": "Facere possimus omnis voluptas assumenda est omnis?", "type": "mc", "exam": {"question": "Facere possimus omnis voluptas assumenda est omnis dolor repellendus temporibus autem quibusdam et aut officiis debitis aut rerum necessitatibus?", "answer": ["A", "B"], "choices": [{"A": "<PERSON><PERSON><PERSON> eveniet ut et voluptates repudiandae sint (correct answer)"}, {"B": "Et molestiae non recusandae itaque earum rerum (correct answer)"}, {"C": "Hic tenetur a sapiente delectus ut aut"}, {"D": "Reiciendis voluptatibus maiores alias consequatur aut"}]}, "vote_distribution": {}}, {"_id": {"$oid": "679e05c962fb0d0bbe484850"}, "url": "/discussions/dummy/view/20050-exam-dummy-exam-3-question-16-discussion/", "code": "dummy-exam-3", "subject": 2, "timestamp": {"$date": "2025-02-01T11:49:00.000Z"}, "date_added": "2024-04-19", "first_sentence": "Perferendis doloribus asperiores repellat sed ut perspiciatis?", "type": "mc", "exam": {"question": "Perferendis doloribus asperiores repellat sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium totam rem?", "answer": ["D"], "choices": [{"A": "A<PERSON>iam eaque ipsa quae ab illo inventore"}, {"B": "Veritatis et quasi architecto beatae vitae dicta"}, {"C": "Sunt explicabo nemo enim ipsam voluptatem quia"}, {"D": "<PERSON><PERSON><PERSON> sit aspernatur aut odit aut fugit (correct answer)"}]}, "vote_distribution": {}}]