const axios = require('axios');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config({ path: '.env.development' });

const API_BASE_URL = process.env.API_URL || 'http://localhost:3016';
const TEST_TOKEN = process.env.TEST_TOKEN || 'test-token';

/**
 * Integration Test: Complete Google Authentication Flow
 * 
 * This test simulates the complete authentication flow from React Native
 * app through the Node.js backend using Google tokens.
 */

class MockReactNativeClient {
  constructor() {
    this.baseUrl = API_BASE_URL;
    this.token = null;
  }

  // Simulate ApiClient.request method
  async request(endpoint, options = {}) {
    const url = `${this.baseUrl}${endpoint}`;
    const config = {
      method: options.method || 'GET',
      url,
      headers: {
        'Content-Type': 'application/json',
        ...(this.token && { 'Authorization': `Bearer ${this.token}` })
      },
      timeout: options.timeout || 10000
    };

    if (options.body) {
      config.data = JSON.parse(options.body);
    }

    try {
      const response = await axios(config);
      
      // Simulate ApiClient response handling for auth endpoints
      if (endpoint.includes('/auth')) {
        return response.data; // Return full response for auth endpoints
      } else {
        return response.data.data !== undefined ? response.data.data : response.data;
      }
    } catch (error) {
      if (error.response) {
        throw new Error(`Request failed: ${error.response.status} - ${error.response.statusText}\n${JSON.stringify(error.response.data)}`);
      }
      throw error;
    }
  }

  // Simulate ApiClient.setAuthToken method
  setAuthToken(token) {
    this.token = token || null;
    console.log(`[MockClient] Auth token ${token ? 'set' : 'cleared'}, length: ${token ? token.length : 0}`);
  }

  // Simulate ApiClient.loginWithGoogle method
  async loginWithGoogle(googleIdToken) {
    console.log('[MockClient] Starting Google login process');
    
    if (!googleIdToken) {
      throw new Error('No Google ID token provided');
    }

    try {
      const response = await this.request('/auth/google', {
        method: 'POST',
        body: JSON.stringify({ idToken: googleIdToken })
      });

      console.log('[MockClient] Received login response:', JSON.stringify(response, null, 2));

      if (!response || !response.success) {
        throw new Error(response?.message || 'Authentication failed');
      }

      if (response.token) {
        // Store the Google token as our auth token
        this.setAuthToken(response.token);
        console.log('[MockClient] Google token stored successfully');
      } else {
        throw new Error('No token received from server');
      }

      return response;
    } catch (error) {
      console.error('[MockClient] Login failed:', error.message);
      throw error;
    }
  }

  // Simulate ApiClient.getUserByGoogleId method
  async getUserByGoogleId(googleId) {
    console.log(`[MockClient] Getting user by Google ID: ${googleId}`);
    
    if (!googleId) {
      throw new Error('Google ID is required');
    }

    return this.request(`/user/google/${encodeURIComponent(googleId)}`, {
      method: 'GET'
    });
  }

  // Simulate ApiClient.getUserProfile method
  async getUserProfile() {
    console.log('[MockClient] Getting user profile');
    return this.request('/user/profile', {
      method: 'GET'
    });
  }

  // Simulate ApiClient.logout method
  async logout() {
    console.log('[MockClient] Logging out');
    
    try {
      if (this.token) {
        await this.request('/auth/logout', {
          method: 'POST'
        });
        console.log('[MockClient] Server logout successful');
      }
    } catch (error) {
      console.error('[MockClient] Server logout failed:', error.message);
    } finally {
      this.setAuthToken(null);
      console.log('[MockClient] Local logout completed');
    }

    return { success: true };
  }
}

// Test scenarios
async function testCompleteAuthFlow() {
  console.log('🚀 Starting Complete Authentication Flow Test\n');

  const client = new MockReactNativeClient();
  let testResults = [];

  // Step 1: Simulate React Native Google Sign-In
  console.log('📱 Step 1: Simulating React Native Google Sign-In');
  try {
    // In real React Native app, this would come from @react-native-google-signin/google-signin
    const mockGoogleSignInResult = {
      idToken: TEST_TOKEN, // Using test token for development
      user: {
        id: 'test-google-id',
        email: '<EMAIL>',
        name: 'Test User',
        photo: 'https://example.com/photo.jpg'
      }
    };

    console.log('   Google Sign-In successful:', mockGoogleSignInResult.user);
    testResults.push({ step: 'Google Sign-In', success: true });

    // Step 2: Send Google ID token to our backend
    console.log('\n🔐 Step 2: Authenticating with backend');
    const loginResponse = await client.loginWithGoogle(mockGoogleSignInResult.idToken);
    
    console.log('   Backend authentication successful');
    console.log('   User data:', loginResponse.user);
    testResults.push({ step: 'Backend Authentication', success: true });

    // Step 3: Make authenticated API calls
    console.log('\n📊 Step 3: Making authenticated API calls');
    
    // Test user profile endpoint
    try {
      const profile = await client.getUserProfile();
      console.log('   ✅ User profile retrieved successfully');
      testResults.push({ step: 'Get User Profile', success: true });
    } catch (error) {
      console.log('   ❌ User profile retrieval failed:', error.message);
      testResults.push({ step: 'Get User Profile', success: false, error: error.message });
    }

    // Test user by Google ID endpoint
    try {
      const userData = await client.getUserByGoogleId('test-google-id');
      console.log('   ✅ User data by Google ID retrieved successfully');
      console.log('   User data keys:', Object.keys(userData.user || {}));
      testResults.push({ step: 'Get User by Google ID', success: true });
    } catch (error) {
      console.log('   ❌ User data by Google ID retrieval failed:', error.message);
      testResults.push({ step: 'Get User by Google ID', success: false, error: error.message });
    }

    // Step 4: Test token persistence (simulate app restart)
    console.log('\n🔄 Step 4: Testing token persistence (simulating app restart)');
    const savedToken = client.token;
    
    // Create new client instance (simulating app restart)
    const newClient = new MockReactNativeClient();
    newClient.setAuthToken(savedToken); // Simulate loading from AsyncStorage
    
    try {
      const profile = await newClient.getUserProfile();
      console.log('   ✅ Token persistence works - authenticated after "restart"');
      testResults.push({ step: 'Token Persistence', success: true });
    } catch (error) {
      console.log('   ❌ Token persistence failed:', error.message);
      testResults.push({ step: 'Token Persistence', success: false, error: error.message });
    }

    // Step 5: Test logout
    console.log('\n🚪 Step 5: Testing logout');
    await client.logout();
    
    // Try to make authenticated request after logout
    try {
      await client.getUserProfile();
      console.log('   ❌ Logout failed - still authenticated');
      testResults.push({ step: 'Logout', success: false, error: 'Still authenticated after logout' });
    } catch (error) {
      console.log('   ✅ Logout successful - no longer authenticated');
      testResults.push({ step: 'Logout', success: true });
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    testResults.push({ step: 'Overall Flow', success: false, error: error.message });
  }

  // Print test summary
  console.log('\n📋 Test Summary:');
  console.log('================');
  
  const successCount = testResults.filter(r => r.success).length;
  const totalCount = testResults.length;
  
  testResults.forEach(result => {
    const status = result.success ? '✅' : '❌';
    console.log(`${status} ${result.step}`);
    if (result.error) {
      console.log(`   Error: ${result.error}`);
    }
  });
  
  console.log(`\nOverall: ${successCount}/${totalCount} tests passed`);
  
  if (successCount === totalCount) {
    console.log('🎉 All tests passed! Google token authentication is working correctly.');
  } else {
    console.log('⚠️  Some tests failed. Please check the implementation.');
  }
}

// Performance test
async function testPerformance() {
  console.log('\n⚡ Performance Test: Token Validation Speed\n');

  const client = new MockReactNativeClient();
  
  // Login first
  await client.loginWithGoogle(TEST_TOKEN);
  
  const iterations = 10;
  const times = [];
  
  for (let i = 0; i < iterations; i++) {
    const start = Date.now();
    try {
      await client.getUserProfile();
      const end = Date.now();
      times.push(end - start);
    } catch (error) {
      console.log(`Request ${i + 1} failed:`, error.message);
    }
  }
  
  if (times.length > 0) {
    const avgTime = times.reduce((a, b) => a + b, 0) / times.length;
    const minTime = Math.min(...times);
    const maxTime = Math.max(...times);
    
    console.log(`Performance Results (${iterations} requests):`);
    console.log(`  Average: ${avgTime.toFixed(2)}ms`);
    console.log(`  Min: ${minTime}ms`);
    console.log(`  Max: ${maxTime}ms`);
    console.log(`  First request (cache miss): ${times[0]}ms`);
    console.log(`  Subsequent requests (cache hit): ${times.slice(1).join('ms, ')}ms`);
  }
}

// Main test runner
async function runIntegrationTests() {
  console.log('🧪 Google Token Authentication Integration Tests');
  console.log('================================================\n');
  
  await testCompleteAuthFlow();
  await testPerformance();
  
  console.log('\n✨ Integration tests completed!');
}

// Export for use in other test files
module.exports = {
  MockReactNativeClient,
  testCompleteAuthFlow,
  testPerformance,
  runIntegrationTests
};

// Run tests if this file is executed directly
if (require.main === module) {
  runIntegrationTests();
}
