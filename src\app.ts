import path from 'path';
import express from 'express';
import cors from 'cors';
import { logger } from './utils/logger';
import routes from './routes';
import * as swaggerUi from 'swagger-ui-express';
import { swaggerSpec } from './config/swagger.config';
import * as dotenvx from '@dotenvx/dotenvx';
import { connectDB } from './config/database';
import { CompressionMiddleware, CompressionUtil } from './utils/compression.util';
import { authDiagnosticsMiddleware, AuthDiagnostics } from './utils/auth-diagnostics.util';

const app = express();

logger.info(`Application starting in ${dotenvx.get('MODE')} mode`);
logger.info(`CORS_ORIGIN: ${dotenvx.get('CORS_ORIGIN')}`);

// Middleware configuration
app.use(cors({
    origin: dotenvx.get('CORS_ORIGIN') || '*',
    methods: ['GET', 'POST', 'PUT', 'DELETE'],
}));
// Increase limit for AI chat history and other large payloads
// Different limits for different routes can be implemented if needed
app.use(express.json({
    limit: '50mb' // Generous limit for AI chat data
}));
app.use(express.urlencoded({
    limit: '50mb',
    extended: true
}));

// Add compression middleware for handling compressed requests and responses
app.use(CompressionMiddleware.handleCompressedRequest());
app.use(CompressionMiddleware.compressResponse()); // Use environment-based threshold

// Add authentication diagnostics middleware (only in development)
app.use(authDiagnosticsMiddleware);

// Log configuration on startup
AuthDiagnostics.logAuthStatus();
CompressionUtil.logCompressionConfig();

// Serve static files from the "public" directory
app.use('/public', express.static(path.join(__dirname, '../public')));

// API routes
app.use('/api', routes);

// Swagger documentation
app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(swaggerSpec));
logger.info(`Swagger UI available at /api-docs`);

// Health check endpoint
app.get('/health', (_req, res) => {
    res.status(200).json({ status: 'ok', environment: dotenvx.get('MODE') });
});

// Authentication debug endpoint (development only)
app.get('/debug/auth', async (req: any, res: any) => {
    if (dotenvx.get('MODE') !== 'development') {
        return res.status(404).json({ error: 'Not found' });
    }

    try {
        const diagnostics = await AuthDiagnostics.diagnoseRequest(req);
        res.json({
            success: true,
            diagnostics,
            config: AuthDiagnostics.getAuthConfig(),
            timestamp: new Date().toISOString(),
            instructions: {
                usage: 'Add ?debug=auth to any API endpoint to get authentication diagnostics',
                example: 'GET /api/user/ai-chats?debug=auth',
                headers: 'Include Authorization: Bearer <your-token> header'
            }
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: 'Diagnostics failed',
            message: error instanceof Error ? error.message : String(error)
        });
    }
});

// Connect to MongoDB
connectDB()
    .then(() => {
        logger.info('Connected to MongoDB');
    })
    .catch((error) => {
        logger.error('Failed to connect to MongoDB', error);
        process.exit(1);
    });

export default app;