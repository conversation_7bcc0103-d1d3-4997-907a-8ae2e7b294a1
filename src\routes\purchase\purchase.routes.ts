import { Router } from 'express';
import { createWooOrder, getCheckoutSession, getMyPurchase, getPurchaseProductList } from './purchase.controller';
import { authenticateToken } from '../../middleware/auth.middleware';

const router = Router();

/**
 * @swagger
 * /api/purchase/my:
 *   get:
 *     tags:
 *       - Purchase
 *     summary: Get user's purchase history
 *     description: Retrieves the authenticated user's purchase history including package and exam details
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 code:
 *                   type: number
 *                   example: 200
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       purchase_id:
 *                         type: string
 *                         example: "507f1f77bcf86cd799439011"
 *                       package_name:
 *                         type: string
 *                         example: "Premium Package"
 *                       package_description:
 *                         type: string
 *                         example: "Access to all premium exams"
 *                       package_id:
 *                         type: string
 *                         example: "PKG001"
 *                       exam_name:
 *                         type: string
 *                         example: "AWS Certified Solutions Architect"
 *                       purchase_date:
 *                         type: string
 *                         format: date-time
 *                       purchase_status:
 *                         type: string
 *                         example: "completed"
 *       401:
 *         description: Unauthorized - Invalid token
 *       400:
 *         description: Bad request
 *       404:
 *         description: Not found
 */

router.get('/my', authenticateToken, getMyPurchase);

/**
 * @swagger
 * /api/purchase/products:
 *   get:
 *     tags:
 *       - Purchase
 *     summary: Get list of purchasable products
 *     description: Retrieves list of products available for purchase from WooCommerce
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 code:
 *                   type: number
 *                   example: 200
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: number
 *                         example: 123
 *                       name:
 *                         type: string
 *                         example: "Premium Package"
 *                       description:
 *                         type: string
 *                         example: "Access to premium content"
 *                       price:
 *                         type: string
 *                         example: "99.99"
 *                       status:
 *                         type: string
 *                         example: "publish"
 *       400:
 *         description: Bad request
 *       404:
 *         description: Products not found
 */

router.get('/products', getPurchaseProductList);

/**
 * @swagger
 * /api/purchase/checkout-session:
 *   post:
 *     tags:
 *       - Purchase
 *     summary: Create checkout session
 *     description: Creates a Stripe checkout session for payment processing
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               price:
 *                 type: number
 *                 example: 99.99
 *               name:
 *                 type: string
 *                 example: "Premium Package"
 *               image_url:
 *                 type: string
 *                 example: "https://example.com/image.jpg"
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 code:
 *                   type: number
 *                   example: 200
 *                 data:
 *                   type: object
 *                   properties:
 *                     checkout_key:
 *                       type: string
 *                       description: Stripe client secret for embedded checkout
 *                       example: "pi_3NqF2L2eZvKYlo2C1KOZgCzM_secret_MvEpqxK0RXwkPwQBPPuXtVcxy"
 *       400:
 *         description: Bad request - Invalid price, name or image URL
 *       404:
 *         description: Checkout session creation failed - Unable to create Stripe session
 */
router.post('/checkout-session', getCheckoutSession);

/**
 * @swagger
 * /api/purchase/create-order:
 *   post:
 *     tags:
 *       - Purchase
 *     summary: Create WooCommerce order
 *     description: Creates a new order in WooCommerce after successful payment
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               session_id:
 *                 type: string
 *                 description: Stripe checkout session ID
 *                 example: "cs_test_a1b2c3d4e5f6g7h8i9j0"
 *               product_id:
 *                 type: string
 *                 description: WooCommerce product ID
 *                 example: "123"
 *             required:
 *               - session_id
 *               - product_id
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 code:
 *                   type: number
 *                   example: 200
 *                 data:
 *                   type: object
 *                   description: WooCommerce order details
 *       404:
 *         description: Order creation failed
 */

router.post('/create-order', authenticateToken, createWooOrder);

export default router;
