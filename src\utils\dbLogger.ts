import { Model } from "mongoose";
import { logger } from './logger';
import { ObjectId } from 'mongodb';

/**
 * Helper function to check if a value is a MongoDB ObjectId
 * @param value The value to check
 * @returns True if the value is a MongoDB ObjectId, false otherwise
 */
const isObjectId = (value: any): boolean => {
    return value &&
        typeof value === 'object' &&
        value.constructor &&
        (value.constructor.name === 'ObjectId' || value.constructor.name === 'ObjectID');
};

/**
 * Recursively converts all MongoDB ObjectId values to string format in the provided data
 * @param data The data to process
 * @returns The data with all ObjectId values converted to strings
 */
const convertObjectIdsToStrings = (data: any): any => {
    // Handle null or undefined
    if (data === null || data === undefined) {
        return data;
    }

    // Handle ObjectId directly
    if (isObjectId(data)) {
        return data.toString();
    }

    // Handle arrays
    if (Array.isArray(data)) {
        return data.map(item => convertObjectIdsToStrings(item));
    }

    // Handle objects
    if (typeof data === 'object') {
        const result: any = {};

        for (const key in data) {
            if (Object.prototype.hasOwnProperty.call(data, key)) {
                // Special handling for _id field
                if (key === '_id' && isObjectId(data[key])) {
                    result[key] = data[key].toString();
                } else {
                    // Recursively process nested objects and arrays
                    result[key] = convertObjectIdsToStrings(data[key]);
                }
            }
        }

        return result;
    }

    // Return primitive values as is
    return data;
};


let queryMetrics: Record<string, number[]> = {};

export const withQueryTiming = <T extends (...args: any[]) => Promise<any>>(
    fn: T,
    operationName: string
) => {
    return async (...args: Parameters<T>): Promise<any> => {
        const start = performance.now();
        let result = await fn(...args);
        const duration = performance.now() - start;

        // Store metrics
        queryMetrics[operationName] = queryMetrics[operationName] || [];
        queryMetrics[operationName].push(duration);

        // Log individual operation
        logger.info(`⏱️ [${operationName}] took ${duration.toFixed(2)}ms`);

        // Convert ObjectId values to strings in the result
        const startConversion = performance.now();
        result = convertObjectIdsToStrings(result);
        const conversionDuration = performance.now() - startConversion;

        // Only log if conversion took a significant amount of time
        if (conversionDuration > 5) {
            logger.debug(`🔄 [${operationName}] ObjectId conversion took ${conversionDuration.toFixed(2)}ms`);
        }

        return result;
    };
};

// Add periodic reporting
/* setInterval(() => {
    logger.info('\n📊 Database Performance Report:');
    Object.entries(queryMetrics).forEach(([operation, timings]) => {
        const avg = timings.reduce((a, b) => a + b, 0) / timings.length;
        logger.info(`  ${operation}:`);
        logger.info(`    Calls: ${timings.length}`);
        logger.info(`    Avg: ${avg.toFixed(2)}ms`);
        logger.info(`    Max: ${Math.max(...timings).toFixed(2)}ms`);
    });
    queryMetrics = {}; // Reset metrics
}, 60_000); // Report every minute */

export const DbOperations = {
    /**
     * Find documents matching the query
     * @param model The Mongoose model
     * @param query The query to match documents
     * @param projection Fields to include or exclude
     * @param options Options like skip and limit
     * @returns Array of documents with ObjectIds converted to strings
     */
    find: (
        model: Model<any>,
        query: any = {},
        projection: any = {},
        options: { skip?: number; limit?: number } = {}
    ): Promise<any[]> =>
        withQueryTiming(
            () => model.find(query)
                .select(projection)
                .skip(options.skip || 0)
                .limit(options.limit || 0)
                .lean()
                .exec(),
            `FIND/${model.modelName}`
        )(),

    /**
     * Find a single document matching the query
     * @param model The Mongoose model
     * @param query The query to match the document
     * @returns The document with ObjectIds converted to strings or null if not found
     */
    findOne: (
        model: Model<any>,
        query: any = {},
        projection: any = {}
    ): Promise<any | null> =>
        withQueryTiming(() => model.findOne(query)
            .select(projection)
            .lean()
            .exec(),
            `FIND/${model.modelName}`)(),

    /**
     * Find a document by its ID
     * @param model The Mongoose model
     * @param id The document ID
     * @returns The document with ObjectIds converted to strings or null if not found
     */
    findById: (model: Model<any>, id: string): Promise<any | null> =>
        withQueryTiming(() => model.findById(id).lean().exec(), `FIND_ID/${model.modelName}`)(),

    /**
     * Update a document by its ID
     * @param model The Mongoose model
     * @param id The document ID
     * @param data The data to update
     * @returns The updated document with ObjectIds converted to strings or null if not found
     */
    update: (model: Model<any>, id: string, data: any): Promise<any | null> =>
        withQueryTiming(() => model.findByIdAndUpdate(id, data, { new: true }).lean().exec(),
            `UPDATE/${model.modelName}`)(),

    /**
     * Create a new document
     * @param model The Mongoose model
     * @param data The document data
     * @returns The created document with ObjectIds converted to strings
     */
    create: (model: Model<any>, data: any): Promise<any> =>
        withQueryTiming(() => model.create(data), `CREATE/${model.modelName}`)(),

    /**
     * Utility function to convert ObjectIds to strings in any data
     * @param data The data to process
     * @returns The data with ObjectIds converted to strings
     */
    convertObjectIds: (data: any): any => convertObjectIdsToStrings(data)
};