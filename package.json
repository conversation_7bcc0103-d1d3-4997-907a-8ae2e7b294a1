{"name": "backend", "version": "1.0.0", "main": "index.js", "scripts": {"test": "node tests/google-auth.test.js", "test:integration": "node tests/integration-test.js", "test:all": "npm run test && npm run test:integration", "build": "dotenvx run -f .env -- nodemon src/app.ts", "start": "dotenvx run -f .env -- nodemon src/app.ts", "dev": "dotenvx run -f .env.development -- nodemon src/app.ts", "dev:local": "dotenvx run -f .env.local -- nodemon src/app.ts"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@dotenvx/dotenvx": "^1.37.0", "@types/cors": "^2.8.17", "@types/node": "^22.10.0", "@types/nodemailer": "^6.4.17", "axios": "^1.7.8", "cacheable": "^1.8.8", "cors": "^2.8.5", "express": "^4.21.2", "fast-json-stringify": "^6.0.1", "google-auth-library": "^10.1.0", "jsonwebtoken": "^9.0.2", "moment": "^2.30.1", "mongodb": "^6.13.0", "mongoose": "^8.15.0", "nodemailer": "^7.0.3", "nodemon": "^3.1.7", "openapi-types": "^7.0.0", "pino": "^9.6.0", "pino-pretty": "^13.0.0", "stripe": "^17.5.0", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "ts-node": "^10.9.2", "typescript": "^5.7.2", "ultimate-express": "^1.3.18", "uuid": "^11.1.0"}, "devDependencies": {"@types/jsonwebtoken": "^9.0.9", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.7", "@types/uuid": "^10.0.0"}}