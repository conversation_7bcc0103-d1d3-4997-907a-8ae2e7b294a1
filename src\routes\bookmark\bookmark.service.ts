import { apiConfig } from '../../config/apiConfig';
import { getCollection } from '../../config/database';
import { IBookmark } from './bookmark.model';
import { cacheable, cacheInvalidator } from '../../utils/cacheable';
import { logger } from '../../utils/logger'

export class BookmarkService {
    @cacheable()
    static async getBookmarksByCode(userId: number, code: string): Promise<IBookmark[] | null> {
        try {
            const collection = await getCollection(apiConfig.databaseName.user, "bookmark");
            const bookmarks = await collection.find({
                user: userId,
                code: code
            }).toArray();
            return bookmarks as IBookmark[] | null;
        } catch (error) {
            console.error('Error fetching bookmarks:', error);
            throw error;
        }
    }

    static async createBookmark(userId: number, code: string, qnaId: number): Promise<IBookmark | null> {
        try {
            const collection = await getCollection(apiConfig.databaseName.user, "bookmark");
            const existingBookmark = await collection.findOne({
                user: userId,
                code: code,
                qna_id: qnaId
            });

            let result;
            if (existingBookmark) {
                result = await collection.findOneAndUpdate(
                    { user: userId, code: code, qna_id: qnaId },
                    { $set: { user: userId, code: code, qna_id: qnaId } },
                    { returnDocument: 'after' }
                );
            } else {
                result = await collection.insertOne({
                    user: userId,
                    code: code,
                    qna_id: qnaId
                });
            }
            logger.debug("result", result);
            await cacheInvalidator.byKey(this.name, 'getBookmarksByCode', [userId, code]);

            return result as IBookmark | null;
        } catch (error) {
            console.error('Service Error creating bookmark:', error);
            throw error;
        }
    }

    static async deleteBookmark(userId: number, code: string, qnaId: number): Promise<boolean> {
        try {
            const collection = await getCollection(apiConfig.databaseName.user, "bookmark");
            await collection.deleteOne({ user: userId, code: code, qna_id: qnaId });
            
            await cacheInvalidator.byKey(this.name, 'getBookmarksByCode', [userId, code]);

            return true;
        } catch (error) {
            console.error('Error deleting bookmark:', error);
            throw error;
        }
    }
} 