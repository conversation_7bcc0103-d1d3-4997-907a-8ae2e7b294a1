import pino from 'pino';
import * as dotenvx from '@dotenvx/dotenvx';

const isProduction = dotenvx.get('MODE') === 'production';

// Create a pino logger for development
const pinoLogger = pino({
    level: 'debug', // Use debug level in development
    transport: isProduction
        ? undefined // No transport in production
        : {
            target: 'pino-pretty', // Pretty print in development
            options: {
                colorize: true,
            },
        },
});

// Create a console-based logger for production
const consoleLogger = {
    debug: console.debug,
    info: console.info,
    warn: console.warn,
    error: console.error,
    fatal: console.error, // Use console.error for fatal logs
    log: console.log, // Add console.log
};

// Export the appropriate logger based on the environment
export const logger = isProduction ? consoleLogger : pinoLogger;