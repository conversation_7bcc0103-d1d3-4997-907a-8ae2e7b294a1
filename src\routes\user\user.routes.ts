import { Router, RequestHandler } from 'express';
import { UserController } from './user.controller';
import { authenticateToken } from '../../middleware/auth.middleware';
import { authenticateJWT } from '../../middleware/jwt.middleware';
import * as dotenvx from '@dotenvx/dotenvx';

const router = Router();
const userController = new UserController();

// Apply authentication middleware to all routes
// Cast the middleware to RequestHandler to avoid TypeScript errors
const auth: RequestHandler = dotenvx.get('USE_JWT_AUTH') === 'true'
  ? authenticateJWT as unknown as <PERSON><PERSON><PERSON>and<PERSON>
  : authenticateToken as unknown as <PERSON>questHand<PERSON>;

/**
 * @swagger
 * /api/user/profile:
 *   get:
 *     summary: Get user profile
 *     tags: [User]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: User profile
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: User not found
 *       500:
 *         description: Server error
 */
router.get('/profile', auth, (req, res) => userController.getUserProfile(req, res));

/**
 * @swagger
 * /api/user/profile:
 *   put:
 *     summary: Update user profile
 *     tags: [User]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               displayName:
 *                 type: string
 *               profilePicture:
 *                 type: string
 *     responses:
 *       200:
 *         description: Updated user profile
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: User not found
 *       500:
 *         description: Server error
 */
router.put('/profile', auth, (req, res) => userController.updateUserProfile(req, res));

/**
 * @swagger
 * /api/user/google/{googleId}:
 *   get:
 *     summary: Get user by Google ID (requires JWT authentication)
 *     tags: [User]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: googleId
 *         required: true
 *         schema:
 *           type: string
 *         description: Google ID of the user
 *     responses:
 *       200:
 *         description: Complete user object
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 user:
 *                   type: object
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: User not found
 *       500:
 *         description: Server error
 */
router.get('/google/:googleId', auth, (req, res) => userController.getUserByGoogleId(req, res));

/**
 * @swagger
 * /api/user/progress:
 *   get:
 *     summary: Get user progress
 *     tags: [User Progress]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: courseId
 *         schema:
 *           type: string
 *         description: Optional course ID to filter progress
 *     responses:
 *       200:
 *         description: User progress
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: User not found
 *       500:
 *         description: Server error
 */
router.get('/progress', auth, (req, res) => userController.getUserProgress(req, res));

/**
 * @swagger
 * /api/user/progress/{examId}/{subject}:
 *   put:
 *     summary: Update user progress for exam questions
 *     tags: [User Progress]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: examId
 *         required: true
 *         schema:
 *           type: string
 *         description: Exam ID (e.g., AIF-C01)
 *       - in: path
 *         name: subject
 *         required: true
 *         schema:
 *           type: string
 *         description: Subject name
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - questionId
 *               - action
 *             properties:
 *               questionId:
 *                 type: string
 *                 description: Question ID
 *                 example: "16"
 *               action:
 *                 type: string
 *                 enum: [browsed, bookmarked, answered]
 *                 description: Type of progress action
 *               isBookmarked:
 *                 type: boolean
 *                 description: Required when action is 'bookmarked'
 *               isCorrect:
 *                 type: boolean
 *                 description: Required when action is 'answered'
 *     responses:
 *       200:
 *         description: Updated user progress
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 progress:
 *                   type: object
 *                   properties:
 *                     browsed:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: string
 *                           timestamp:
 *                             type: string
 *                             format: date-time
 *                     bookmarked:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: string
 *                           timestamp:
 *                             type: string
 *                             format: date-time
 *                     correct:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: string
 *                           timestamp:
 *                             type: string
 *                             format: date-time
 *                     incorrect:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: string
 *                           timestamp:
 *                             type: string
 *                             format: date-time
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: User not found
 *       500:
 *         description: Server error
 */
router.put('/progress/:examId/:subject', auth, (req, res) => userController.updateUserProgress(req, res));

/**
 * @swagger
 * /api/user/progress/{examId}:
 *   put:
 *     summary: Bulk update user progress for an entire exam
 *     tags: [User Progress]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: examId
 *         required: true
 *         schema:
 *           type: string
 *         description: Exam ID (e.g., AIF-C01)
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             description: Progress data organized by subject
 *             additionalProperties:
 *               type: object
 *               properties:
 *                 browsed:
 *                   type: array
 *                   items:
 *                     oneOf:
 *                       - type: string
 *                         description: Question ID
 *                       - type: object
 *                         properties:
 *                           id:
 *                             type: string
 *                           timestamp:
 *                             type: string
 *                             format: date-time
 *                 bookmarked:
 *                   type: array
 *                   items:
 *                     oneOf:
 *                       - type: string
 *                         description: Question ID
 *                       - type: object
 *                         properties:
 *                           id:
 *                             type: string
 *                           timestamp:
 *                             type: string
 *                             format: date-time
 *                 correct:
 *                   type: array
 *                   items:
 *                     oneOf:
 *                       - type: string
 *                         description: Question ID
 *                       - type: object
 *                         properties:
 *                           id:
 *                             type: string
 *                           timestamp:
 *                             type: string
 *                             format: date-time
 *                 incorrect:
 *                   type: array
 *                   items:
 *                     oneOf:
 *                       - type: string
 *                         description: Question ID
 *                       - type: object
 *                         properties:
 *                           id:
 *                             type: string
 *                           timestamp:
 *                             type: string
 *                             format: date-time
 *             example:
 *               "Machine Learning":
 *                 browsed: ["16", "23", "40"]
 *                 bookmarked: ["16"]
 *                 correct: ["16", "40"]
 *                 incorrect: ["23"]
 *               "Data Engineering":
 *                 browsed: ["60", "61"]
 *                 bookmarked: []
 *                 correct: ["60"]
 *                 incorrect: ["61"]
 *     responses:
 *       200:
 *         description: Updated exam progress
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 progress:
 *                   type: object
 *                   description: Complete exam progress organized by subject
 *                   additionalProperties:
 *                     type: object
 *                     properties:
 *                       browsed:
 *                         type: array
 *                         items:
 *                           type: object
 *                           properties:
 *                             id:
 *                               type: string
 *                             timestamp:
 *                               type: string
 *                               format: date-time
 *                       bookmarked:
 *                         type: array
 *                         items:
 *                           type: object
 *                           properties:
 *                             id:
 *                               type: string
 *                             timestamp:
 *                               type: string
 *                               format: date-time
 *                       correct:
 *                         type: array
 *                         items:
 *                           type: object
 *                           properties:
 *                             id:
 *                               type: string
 *                             timestamp:
 *                               type: string
 *                               format: date-time
 *                       incorrect:
 *                         type: array
 *                         items:
 *                           type: object
 *                           properties:
 *                             id:
 *                               type: string
 *                             timestamp:
 *                               type: string
 *                               format: date-time
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: User not found
 *       500:
 *         description: Server error
 */
router.put('/progress/:examId', auth, (req, res) => userController.updateUserProgressBulk(req, res));

/**
 * @swagger
 * /api/user/quiz-results:
 *   get:
 *     summary: Get user quiz results
 *     tags: [Quiz Results]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: quizId
 *         schema:
 *           type: string
 *         description: Optional quiz ID to filter results
 *     responses:
 *       200:
 *         description: User quiz results
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: User not found
 *       500:
 *         description: Server error
 */
router.get('/quiz-results', auth, (req, res) => userController.getQuizResults(req, res));

/**
 * @swagger
 * /api/user/quiz-results:
 *   post:
 *     summary: Submit a quiz result
 *     tags: [Quiz Results]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - quizId
 *               - examCode
 *               - score
 *               - totalQuestions
 *               - correctAnswers
 *               - timeTaken
 *               - answers
 *               - userId
 *               - resultId
 *             properties:
 *               quizId:
 *                 type: string
 *                 example: "AIF-C01"
 *               examCode:
 *                 type: string
 *                 example: "AIF-C01"
 *               score:
 *                 type: number
 *                 example: 80
 *               totalQuestions:
 *                 type: number
 *                 example: 10
 *               correctAnswers:
 *                 type: number
 *                 example: 8
 *               timeTaken:
 *                 type: number
 *                 example: 16
 *               completedAt:
 *                 type: string
 *                 format: date-time
 *                 example: "2025-05-24T13:09:53.603Z"
 *               answers:
 *                 type: object
 *                 additionalProperties:
 *                   type: array
 *                   items:
 *                     type: string
 *                 example:
 *                   "16": ["C"]
 *                   "23": ["C"]
 *                   "40": ["C"]
 *               flaggedQuestions:
 *                 type: array
 *                 items:
 *                   type: string
 *                 example: []
 *               userId:
 *                 type: string
 *                 example: "6831a7c101f6273b697a3862"
 *               resultId:
 *                 type: string
 *                 example: "result-1748092193603"
 *     responses:
 *       200:
 *         description: Saved quiz result
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: User not found
 *       500:
 *         description: Server error
 */
router.post('/quiz-results', auth, (req, res) => userController.submitQuizResult(req, res));

/**
 * @swagger
 * /api/user/purchases:
 *   get:
 *     summary: Get user purchases
 *     tags: [Purchases]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [active, expired, refunded]
 *         description: Optional status filter
 *     responses:
 *       200:
 *         description: User purchases
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: User not found
 *       500:
 *         description: Server error
 */
router.get('/purchases', auth, (req, res) => userController.getUserPurchases(req, res));

/**
 * @swagger
 * /api/user/purchases/verify:
 *   post:
 *     summary: Verify and record a purchase
 *     tags: [Purchases]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - productId
 *               - transactionId
 *               - platform
 *               - receipt
 *             properties:
 *               productId:
 *                 type: string
 *               transactionId:
 *                 type: string
 *               platform:
 *                 type: string
 *                 enum: [ios, android, web]
 *               receipt:
 *                 type: string
 *     responses:
 *       200:
 *         description: Verified purchase data
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: User not found
 *       500:
 *         description: Server error
 */
router.post('/purchases/verify', auth, (req, res) => userController.verifyPurchase(req, res));

/**
 * @swagger
 * /api/user/ai-credits:
 *   get:
 *     summary: Get user AI credits
 *     tags: [AI Credits]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: User AI credits
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: User not found
 *       500:
 *         description: Server error
 */
router.get('/ai-credits', auth, (req, res) => userController.getAICredits(req, res));

/**
 * @swagger
 * /api/user/ai-credits/transactions:
 *   get:
 *     summary: Get AI credit transaction history
 *     tags: [AI Credits]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 20
 *         description: Number of transactions to return
 *       - in: query
 *         name: offset
 *         schema:
 *           type: integer
 *           default: 0
 *         description: Offset for pagination
 *     responses:
 *       200:
 *         description: AI credit transactions
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: User not found
 *       500:
 *         description: Server error
 */
router.get('/ai-credits/transactions', auth, (req, res) => userController.getAICreditTransactions(req, res));

/**
 * @swagger
 * /api/user/ai-credits/purchase:
 *   post:
 *     summary: Purchase AI credits
 *     tags: [AI Credits]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - amount
 *               - paymentMethod
 *             properties:
 *               amount:
 *                 type: number
 *               paymentMethod:
 *                 type: string
 *               paymentDetails:
 *                 type: object
 *     responses:
 *       200:
 *         description: Updated AI credits
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: User not found
 *       500:
 *         description: Server error
 */
router.post('/ai-credits/purchase', auth, (req, res) => userController.purchaseAICredits(req, res));

/**
 * @swagger
 * /api/user/ai-chats:
 *   get:
 *     summary: Get complete user AI chat history
 *     tags: [AI Chat]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Complete AI chat history organized by question IDs
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 chatHistory:
 *                   type: object
 *                   additionalProperties:
 *                     type: object
 *                     properties:
 *                       messages:
 *                         type: array
 *                         items:
 *                           type: object
 *                           properties:
 *                             id:
 *                               type: string
 *                             choices:
 *                               type: array
 *                               items:
 *                                 type: object
 *                                 properties:
 *                                   index:
 *                                     type: integer
 *                                   message:
 *                                     type: object
 *                                     properties:
 *                                       role:
 *                                         type: string
 *                                         enum: [user, assistant]
 *                                       content:
 *                                         type: string
 *                                       isLoading:
 *                                         type: boolean
 *                                       metadata:
 *                                         type: object
 *                                   logprobs:
 *                                     type: object
 *                                   finish_reason:
 *                                     type: string
 *                       quickReplies:
 *                         type: array
 *                         items:
 *                           type: string
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: User not found
 *       500:
 *         description: Server error
 */
router.get('/ai-chats', auth, (req, res) => userController.getAIChatHistory(req, res));

/**
 * @swagger
 * /api/user/ai-chats:
 *   put:
 *     summary: Update complete user AI chat history
 *     tags: [AI Chat]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             description: Complete AI chat history organized by question IDs
 *             additionalProperties:
 *               type: object
 *               properties:
 *                 messages:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                       choices:
 *                         type: array
 *                         items:
 *                           type: object
 *                           properties:
 *                             index:
 *                               type: integer
 *                             message:
 *                               type: object
 *                               properties:
 *                                 role:
 *                                   type: string
 *                                   enum: [user, assistant]
 *                                 content:
 *                                   type: string
 *                                 isLoading:
 *                                   type: boolean
 *                                 metadata:
 *                                   type: object
 *                             logprobs:
 *                               type: object
 *                             finish_reason:
 *                               type: string
 *                 quickReplies:
 *                   type: array
 *                   items:
 *                     type: string
 *             example:
 *               "679e05c962fb0d0bbe484926":
 *                 messages:
 *                   - id: "welcome-message"
 *                     choices:
 *                       - index: 0
 *                         message:
 *                           role: "assistant"
 *                           content: "I'm here to help with this Q&A question. What would you like to know?"
 *                           isLoading: false
 *                 quickReplies:
 *                   - "Explain why the answer is correct and others are wrong."
 *                   - "Give an example to clarify the answer."
 *                   - "Share references for the correct answer."
 *     responses:
 *       200:
 *         description: Updated AI chat history
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 chatHistory:
 *                   type: object
 *                   description: Updated complete AI chat history
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: User not found
 *       500:
 *         description: Server error
 */
router.put('/ai-chats', auth, (req, res) => userController.updateAIChatHistory(req, res));

/**
 * @swagger
 * /api/user/ai-chats:
 *   post:
 *     summary: Create a new AI chat
 *     tags: [AI Chat]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               title:
 *                 type: string
 *     responses:
 *       200:
 *         description: New chat data
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: User not found
 *       500:
 *         description: Server error
 */
router.post('/ai-chats', auth, (req, res) => userController.createAIChat(req, res));

/**
 * @swagger
 * /api/user/ai-chats/{chatId}:
 *   get:
 *     summary: Get a specific AI chat
 *     tags: [AI Chat]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: chatId
 *         required: true
 *         schema:
 *           type: string
 *         description: Chat ID
 *     responses:
 *       200:
 *         description: Chat data including messages
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Chat not found
 *       500:
 *         description: Server error
 */
router.get('/ai-chats/:chatId', auth, (req, res) => userController.getAIChat(req, res));

/**
 * @swagger
 * /api/user/ai-chats/{chatId}:
 *   delete:
 *     summary: Delete an AI chat
 *     tags: [AI Chat]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: chatId
 *         required: true
 *         schema:
 *           type: string
 *         description: Chat ID
 *     responses:
 *       200:
 *         description: Deletion confirmation
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Chat not found
 *       500:
 *         description: Server error
 */
router.delete('/ai-chats/:chatId', auth, (req, res) => userController.deleteAIChat(req, res));

/**
 * @swagger
 * /api/user/ai-chats/{chatId}/messages:
 *   post:
 *     summary: Send a message in an AI chat
 *     tags: [AI Chat]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: chatId
 *         required: true
 *         schema:
 *           type: string
 *         description: Chat ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - message
 *             properties:
 *               message:
 *                 type: string
 *     responses:
 *       200:
 *         description: Updated chat with AI response
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Chat not found
 *       500:
 *         description: Server error
 */
router.post('/ai-chats/:chatId/messages', auth, (req, res) => userController.sendAIChatMessage(req, res));

/**
 * @swagger
 * /api/user/ai-chats/{examId}/{qnaId}/messages:
 *   put:
 *     summary: Add messages to AI chat history for a specific exam and QnA
 *     tags: [AI Chat]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: examId
 *         required: true
 *         schema:
 *           type: string
 *         description: Exam ID (e.g., aws-certified-ai-practitioner-aif-c01)
 *         example: "aws-certified-ai-practitioner-aif-c01"
 *       - in: path
 *         name: qnaId
 *         required: true
 *         schema:
 *           type: string
 *         description: QnA ID (e.g., 679e05c962fb0d0bbe484926)
 *         example: "679e05c962fb0d0bbe484926"
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - messages
 *             properties:
 *               messages:
 *                 type: array
 *                 description: Array of message objects to append
 *                 items:
 *                   type: object
 *                   required:
 *                     - id
 *                     - choices
 *                   properties:
 *                     id:
 *                       type: string
 *                       description: Unique message ID
 *                       example: "1748057574000"
 *                     choices:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           index:
 *                             type: integer
 *                             example: 0
 *                           message:
 *                             type: object
 *                             required:
 *                               - role
 *                               - content
 *                             properties:
 *                               role:
 *                                 type: string
 *                                 enum: [user, assistant]
 *                                 example: "user"
 *                               content:
 *                                 type: string
 *                                 example: "Explain why the answer is correct and others are wrong."
 *                               isLoading:
 *                                 type: boolean
 *                                 example: false
 *                               metadata:
 *                                 type: object
 *                                 description: Additional metadata for the message
 *                           logprobs:
 *                             type: object
 *                             nullable: true
 *                           finish_reason:
 *                             type: string
 *                             example: "stop"
 *               quickReplies:
 *                 type: array
 *                 description: Optional quick reply suggestions to update
 *                 items:
 *                   type: string
 *                 example: ["What are common techniques?", "How does this compare?"]
 *             example:
 *               messages:
 *                 - id: "1748057574000"
 *                   choices:
 *                     - index: 0
 *                       message:
 *                         role: "user"
 *                         content: "Explain why the answer is correct and others are wrong."
 *                       logprobs: null
 *                       finish_reason: "stop"
 *               quickReplies:
 *                 - "What are common techniques?"
 *                 - "How does this compare?"
 *     responses:
 *       200:
 *         description: Messages successfully added to AI chat history
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 messageCount:
 *                   type: integer
 *                   description: Total number of messages after addition
 *                   example: 5
 *                 addedCount:
 *                   type: integer
 *                   description: Number of messages added in this request
 *                   example: 2
 *                 examId:
 *                   type: string
 *                   example: "aws-certified-ai-practitioner-aif-c01"
 *                 qnaId:
 *                   type: string
 *                   example: "679e05c962fb0d0bbe484926"
 *       400:
 *         description: Bad request - invalid examId, qnaId, or message format
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: User not found
 *       500:
 *         description: Server error
 */
router.put('/ai-chats/:examId/:qnaId/messages', auth, (req, res) => userController.addAIChatMessages(req, res));

/**
 * @swagger
 * /api/user/login-history:
 *   get:
 *     summary: Get user login history
 *     tags: [Login History]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: Number of login records to return
 *       - in: query
 *         name: offset
 *         schema:
 *           type: integer
 *           default: 0
 *         description: Offset for pagination
 *     responses:
 *       200:
 *         description: User login history
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: User not found
 *       500:
 *         description: Server error
 */
router.get('/login-history', auth, (req, res) => userController.getLoginHistory(req, res));

export default router;