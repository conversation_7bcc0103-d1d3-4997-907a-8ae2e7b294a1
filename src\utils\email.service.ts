import nodemailer from 'nodemailer';
import { config } from 'dotenv';

config();

interface EmailOptions {
  to: string;
  subject: string;
  text: string;
  html?: string;
}

export class EmailService {
  private static transporter = nodemailer.createTransport({
    host: process.env.GMAIL_HOST,
    port: Number(process.env.GMAIL_PORT),
    secure: process.env.GMAIL_SECURE === 'true',
    auth: {
      user: process.env.GMAIL_USER,
      pass: process.env.GMAIL_PASSWORD,
    },
  });

  static async sendEmail(options: EmailOptions): Promise<void> {
    try {
      await this.transporter.sendMail({
        from: `"Q&A Feedback System" <${process.env.GMAIL_USER}>`,
        ...options
      });
    } catch (error) {
      console.error('Error sending email:', error);
      throw new Error('Failed to send email');
    }
  }

  static async sendFeedbackEmail(
    userId: string,
    qnaId: string,
    content: string
  ): Promise<void> {
    const subject = `New Q&A Feedback Report - QNA ID: ${qnaId}`;
    const text = `
      User ID: ${userId}
      Q&A ID: ${qnaId}
      Feedback Content:
      ${content}
    `;
    
    const html = `
      <h1>New Q&A Feedback Report</h1>
      <p><strong>User ID:</strong> ${userId}</p>
      <p><strong>Q&A ID:</strong> ${qnaId}</p>
      <h3>Feedback Content:</h3>
      <p>${content}</p>
    `;

    await this.sendEmail({
      to: process.env.GMAIL_USER!,
      subject,
      text,
      html
    });
  }
}