import { ObjectId } from 'mongodb';
import { DbOperations } from './dbLogger';

describe('DbOperations', () => {
    describe('convertObjectIds', () => {
        it('should convert ObjectId to string', () => {
            const objectId = new ObjectId('679e05c962fb0d0bbe485cdb');
            const result = DbOperations.convertObjectIds(objectId);
            expect(result).toBe('679e05c962fb0d0bbe485cdb');
        });

        it('should convert ObjectId in object to string', () => {
            const data = {
                _id: new ObjectId('679e05c962fb0d0bbe485cdb'),
                name: 'Test'
            };
            const result = DbOperations.convertObjectIds(data);
            expect(result._id).toBe('679e05c962fb0d0bbe485cdb');
            expect(result.name).toBe('Test');
        });

        it('should convert ObjectId in nested object to string', () => {
            const data = {
                _id: new ObjectId('679e05c962fb0d0bbe485cdb'),
                name: 'Test',
                nested: {
                    _id: new ObjectId('679e05c962fb0d0bbe485cdc'),
                    value: 'Nested'
                }
            };
            const result = DbOperations.convertObjectIds(data);
            expect(result._id).toBe('679e05c962fb0d0bbe485cdb');
            expect(result.nested._id).toBe('679e05c962fb0d0bbe485cdc');
        });

        it('should convert ObjectId in array to string', () => {
            const data = [
                new ObjectId('679e05c962fb0d0bbe485cdb'),
                new ObjectId('679e05c962fb0d0bbe485cdc')
            ];
            const result = DbOperations.convertObjectIds(data);
            expect(result[0]).toBe('679e05c962fb0d0bbe485cdb');
            expect(result[1]).toBe('679e05c962fb0d0bbe485cdc');
        });

        it('should convert ObjectId in array of objects to string', () => {
            const data = [
                {
                    _id: new ObjectId('679e05c962fb0d0bbe485cdb'),
                    name: 'Test 1'
                },
                {
                    _id: new ObjectId('679e05c962fb0d0bbe485cdc'),
                    name: 'Test 2'
                }
            ];
            const result = DbOperations.convertObjectIds(data);
            expect(result[0]._id).toBe('679e05c962fb0d0bbe485cdb');
            expect(result[1]._id).toBe('679e05c962fb0d0bbe485cdc');
        });

        it('should handle null and undefined', () => {
            expect(DbOperations.convertObjectIds(null)).toBeNull();
            expect(DbOperations.convertObjectIds(undefined)).toBeUndefined();
        });

        it('should handle primitive values', () => {
            expect(DbOperations.convertObjectIds('test')).toBe('test');
            expect(DbOperations.convertObjectIds(123)).toBe(123);
            expect(DbOperations.convertObjectIds(true)).toBe(true);
        });

        it('should handle complex nested structures', () => {
            const data = {
                _id: new ObjectId('679e05c962fb0d0bbe485cdb'),
                items: [
                    {
                        _id: new ObjectId('679e05c962fb0d0bbe485cdc'),
                        refs: [
                            new ObjectId('679e05c962fb0d0bbe485cdd'),
                            {
                                _id: new ObjectId('679e05c962fb0d0bbe485cde')
                            }
                        ]
                    }
                ],
                metadata: {
                    created_by: {
                        _id: new ObjectId('679e05c962fb0d0bbe485cdf')
                    }
                }
            };
            
            const result = DbOperations.convertObjectIds(data);
            
            expect(result._id).toBe('679e05c962fb0d0bbe485cdb');
            expect(result.items[0]._id).toBe('679e05c962fb0d0bbe485cdc');
            expect(result.items[0].refs[0]).toBe('679e05c962fb0d0bbe485cdd');
            expect(result.items[0].refs[1]._id).toBe('679e05c962fb0d0bbe485cde');
            expect(result.metadata.created_by._id).toBe('679e05c962fb0d0bbe485cdf');
        });
    });
});
