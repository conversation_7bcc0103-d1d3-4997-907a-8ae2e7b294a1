
import { Router } from 'express';
import { authenticateToken } from '../../middleware/auth.middleware';
import { askAI, askAISimple, getAIChats, saveAIChatHistory } from './ai.controller';

const router = Router();


/**
 * @swagger
 * tags:
 *   - name: AI
 *     description: Artificial Intelligence operations
 * components:
 *   securitySchemes:
 *     bearerAuth:
 *       type: http
 *       scheme: bearer
 *       bearerFormat: JWT
 *       description: >
 *         For testing in non-production environments, use:  
 *         **test-token**
 * 
 * /api/ai/askAi:
 *   post:
 *     summary: Ask AI a question
 *     tags: [AI]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               question:
 *                 type: string
 *                 example: "Explain quantum computing in simple terms"
 *                 description: The question to ask the AI
 *               type:
 *                 type: number
 *                 enum: [1, 2]
 *                 example: 1
 *                 description: |
 *                   Type of conversation flow:
 *                   - 1 = Initial question
 *                   - 2 = Follow-up question
 *               code:
 *                 type: string
 *                 example: "PHY101"
 *                 description: Unique exam/course code
 *               qna:
 *                 type: string
 *                 example: "Q1"
 *                 description: Unique question identifier
 *               previous_conversations:
 *                 type: array
 *                 example:
 *                   - question: "What's the basic unit of quantum information?"
 *                     answer: "The basic unit is called a qubit..."
 *                 description: Conversation history for context
 *                 items:
 *                   type: object
 *                   properties:
 *                     question:
 *                       type: string
 *                     answer:
 *                       type: string
 *             required:
 *               - question
 *               - type
 *     responses:
 *       200:
 *         description: AI response received successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 code:
 *                   type: number
 *                   example: 200
 *                 data:
 *                   type: object
 *                   properties:
 *                     answers:
 *                       type: string
 *                       example: "Quantum computing uses qubits..."
 *                     confidence:
 *                       type: number
 *                       example: 9
 *                     explanation:
 *                       type: string
 *                       example: "Qubits can exist in superposition states..."
 *                     followupQuestions:
 *                       type: array
 *                       items:
 *                         type: string
 *                       example:
 *                         - "How do qubits differ from classical bits?"
 *                         - "What are practical applications of quantum computing?"
 *       401:
 *         description: Unauthorized (Invalid or missing token)
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 code:
 *                   type: number
 *                   example: 500
 *                 error:
 *                   type: string
 *                   example: "Failed to get AI response"
 */
router.post('/askAi', authenticateToken, askAI);

/**
 * @swagger
 * /api/ai/saveAIChatHistory:
 *   post:
 *     tags: [AI]
 *     summary: Save AI chat history
 *     description: Store user's AI chat interaction history
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required: [userId, code, qna, question, answer]
 *             properties:
 *               userId:
 *                 type: integer
 *                 example: 12345
 *               code:
 *                 type: string
 *                 example: "EXAM-2023-MATH-01"
 *               qna:
 *                 type: string
 *                 example: "algebra-001"
 *               question:
 *                 type: string
 *                 example: "What is the quadratic formula?"
 *               answer:
 *                 type: string
 *                 example: "The quadratic formula is x = [-b ± √(b²-4ac)]/(2a)"
 *           examples:
 *             default:
 *               value: {
 *                 "userId": 12345,
 *                 "code": "EXAM-2023-MATH-01",
 *                 "qna": "algebra-001",
 *                 "question": "What is the quadratic formula?",
 *                 "answer": "The quadratic formula is x = [-b ± √(b²-4ac)]/(2a)"
 *               }
 *     responses:
 *       200:
 *         description: Success response
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Response'
 *       400:
 *         description: Invalid parameters
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.post('/saveAIChatHistory', authenticateToken, saveAIChatHistory);

/**
 * @swagger
 * /api/ai/getAIChats:
 *   post:
 *     tags:
 *       - AI
 *     summary: Get AI chat history
 *     description: Retrieves chat history between user and AI for a specific exam code and question
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               code:
 *                 type: string
 *                 description: The exam code
 *               qna:
 *                 type: string
 *                 description: The question identifier
 *             required:
 *               - code
 *               - qna
 *     responses:
 *       200:
 *         description: Chat history retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 code:
 *                   type: number
 *                   example: 200
 *                 data:
 *                   type: object
 *                   properties:
 *                     user:
 *                       type: number
 *                     code:
 *                       type: string
 *                     qna:
 *                       type: string
 *                     history:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           question:
 *                             type: string
 *                           response:
 *                             type: string
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 code:
 *                   type: number
 *                   example: 500
 *                 error:
 *                   type: string
 *                   example: "Failed to retrieve chat history"
 */

router.post('/getAIChats', authenticateToken, getAIChats);

/**
 * @swagger
 * /api/ai/askAISimple:
 *   post:
 *     summary: Simple AI question answering
 *     tags: [AI]
 *     description: Simplified version of askAI that returns a fixed response in development
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               question:
 *                 type: string
 *                 example: "Who invented the light bulb?"
 *             required:
 *               - question
 *     responses:
 *       200:
 *         description: AI response received successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 code:
 *                   type: number
 *                 data:
 *                   type: object
 *                   properties:
 *                     answer:
 *                       type: string
 *                     follow_up_questions:
 *                       type: array
 *                       items:
 *                         type: string
 *       400:
 *         description: Bad request
 *       500:
 *         description: Server error
 */
router.post('/askAISimple', /* authenticateToken, */ askAISimple);

export default router;
