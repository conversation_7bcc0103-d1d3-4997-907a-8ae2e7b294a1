import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { logger } from '../utils/logger';
import * as dotenvx from '@dotenvx/dotenvx';

interface AuthenticatedRequest extends Request {
  user_id?: string;
}

export const authenticateJWT = (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  logger.debug('Starting JWT authentication');
  
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];
  
  if (!token) {
    logger.warn('No token provided');
    return res.status(401).json({ success: false, message: 'Unauthorized: No token provided' });
  }

  // For testing in non-production environments
  if (dotenvx.get('MODE') !== 'production' && token === 'test-token') {
    logger.debug('Using test token for authentication in non-production mode');
    req.user_id = "-1"; // Test user ID
    return next();
  }

  try {
    const secretKey = dotenvx.get('JWT_SECRET') || 'your-secret-key';
    const jwtSecret = Buffer.from(secretKey, 'utf-8');
    const decoded = jwt.verify(token, jwtSecret) as { userId: string };
    
    req.user_id = decoded.userId;
    logger.debug({ userId: req.user_id }, 'JWT authentication successful');
    next();
  } catch (error) {
    logger.error({ error }, 'JWT authentication failed');
    return res.status(401).json({ success: false, message: 'Unauthorized: Invalid token' });
  }
};