import { Schema } from "mongoose";

export interface IAIChat extends Document {
    user: number;
    code: string;
    qna: string;
    history: IAIChatHistory[];
}

export interface IAIChatHistory {
    question: string;
    response: string;
}


export const AIChatSchema = new Schema<IAIChat>({
    user: { type: Number, required: true },
    code: { type: String, required: true },
    qna: { type: String, required: true },
    history: { type: [Object], required: true }
}, {
    timestamps: true,
    versionKey: false
});