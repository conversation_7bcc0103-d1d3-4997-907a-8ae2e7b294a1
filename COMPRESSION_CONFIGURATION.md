# Compression Configuration Guide

## Overview

The application now supports configurable compression for both server-side (Node.js/Express) and client-side (React Native) operations. This allows you to enable/disable compression based on your deployment environment and performance requirements.

## Environment Variables

### Server-Side Configuration (.env files)

Add these variables to your `.env`, `.env.development`, and `.env.local` files:

```bash
# Compression Configuration
ENABLE_COMPRESSION=true                    # Master switch for all compression
ENABLE_REQUEST_COMPRESSION=true           # Enable decompression of incoming requests
ENABLE_RESPONSE_COMPRESSION=true          # Enable compression of outgoing responses
COMPRESSION_THRESHOLD=5120                # Size threshold in bytes (5KB default)
```

### Variable Details

| Variable | Default | Description |
|----------|---------|-------------|
| `ENABLE_COMPRESSION` | `true` | Master switch - disables all compression if `false` |
| `ENABLE_REQUEST_COMPRESSION` | `true` | Controls decompression of compressed client requests |
| `ENABLE_RESPONSE_COMPRESSION` | `true` | Controls compression of server responses |
| `COMPRESSION_THRESHOLD` | `1024` | Minimum size in bytes before compression is applied |

### Inheritance Behavior

- If `ENABLE_COMPRESSION=false`, all compression is disabled regardless of other settings
- If `ENABLE_REQUEST_COMPRESSION` is not set, it inherits from `ENABLE_COMPRESSION`
- If `ENABLE_RESPONSE_COMPRESSION` is not set, it inherits from `ENABLE_COMPRESSION`
- If `COMPRESSION_THRESHOLD` is not set, defaults to 1024 bytes (1KB)

## Configuration Examples

### 1. **Full Compression Enabled** (Recommended for Production)
```bash
ENABLE_COMPRESSION=true
ENABLE_REQUEST_COMPRESSION=true
ENABLE_RESPONSE_COMPRESSION=true
COMPRESSION_THRESHOLD=5120  # 5KB threshold
```

### 2. **Compression Disabled** (For Development/Debugging)
```bash
ENABLE_COMPRESSION=false
# All other compression settings are ignored
```

### 3. **Response-Only Compression** (Reduce bandwidth usage)
```bash
ENABLE_COMPRESSION=true
ENABLE_REQUEST_COMPRESSION=false
ENABLE_RESPONSE_COMPRESSION=true
COMPRESSION_THRESHOLD=1024  # 1KB threshold
```

### 4. **High Threshold** (Compress only very large payloads)
```bash
ENABLE_COMPRESSION=true
COMPRESSION_THRESHOLD=51200  # 50KB threshold
```

## Client-Side Configuration (React Native)

### Global Configuration

For React Native, you can configure compression at build time by setting global variables:

```javascript
// In your app's entry point or config
global.__COMPRESSION_DISABLED__ = false;  // Set to true to disable compression
global.__COMPRESSION_THRESHOLD__ = 5120;  // 5KB threshold
```

### Runtime Configuration

You can also check and log the current configuration:

```javascript
import { CompressionUtil } from './CompressionUtil';

// Log current configuration
CompressionUtil.logCompressionConfig();

// Check if compression is enabled
if (CompressionUtil.isCompressionEnabled()) {
  console.log('Compression is enabled');
} else {
  console.log('Compression is disabled');
}
```

## Server Startup Logs

When the server starts, you'll see compression configuration logs:

```
[INFO] Compression Configuration
  compressionEnabled: true
  requestCompressionEnabled: true
  responseCompressionEnabled: true
  compressionThreshold: 5120
  compressionThresholdFormatted: "5 KB"
```

## API Behavior

### When Compression is Enabled

**Client Request:**
```javascript
// Large data automatically compressed before sending
const result = await apiClient.updateAIChatHistory(largeData);
```

**Server Response:**
```javascript
// Large responses automatically compressed before sending
res.json(largeResponseData); // Automatically compressed if > threshold
```

### When Compression is Disabled

**Client Request:**
```javascript
// Data sent uncompressed regardless of size
const result = await apiClient.updateAIChatHistory(largeData);
```

**Server Response:**
```javascript
// Responses sent uncompressed regardless of size
res.json(largeResponseData); // Always uncompressed
```

## Performance Impact

### Compression Enabled
- **Pros**: Reduced bandwidth usage, faster transfer for large payloads
- **Cons**: CPU overhead for compression/decompression
- **Best for**: Production environments with large data transfers

### Compression Disabled
- **Pros**: No CPU overhead, easier debugging, simpler troubleshooting
- **Cons**: Higher bandwidth usage, slower transfers for large payloads
- **Best for**: Development environments, debugging, low-latency requirements

## Debugging

### Server-Side Debugging

Enable debug logging to see compression activity:

```bash
LOG_LEVEL=debug npm run dev
```

You'll see logs like:
```
[DEBUG] Compression enabled for response: 45.2 KB → 12.8 KB (71.7% reduction)
[DEBUG] Request decompression: 12.8 KB → 45.2 KB
[DEBUG] Compression disabled via environment configuration
```

### Client-Side Debugging

The React Native client logs compression activity:

```
[CompressionUtil] Configuration: { compressionEnabled: true, compressionThreshold: 5120 }
[ApiClient] AI chat history compressed: 45.2 KB → 12.8 KB (71.7% reduction)
[ApiClient] Compression disabled via configuration
```

## Testing Different Configurations

### Test Compression Disabled
```bash
# In .env.development
ENABLE_COMPRESSION=false

# Restart server
npm run dev

# Test API calls - should see no compression logs
```

### Test High Threshold
```bash
# In .env.development
COMPRESSION_THRESHOLD=102400  # 100KB

# Restart server
npm run dev

# Small payloads won't be compressed
```

### Test Response-Only Compression
```bash
# In .env.development
ENABLE_REQUEST_COMPRESSION=false
ENABLE_RESPONSE_COMPRESSION=true

# Client requests won't be compressed, but server responses will be
```

## Migration Guide

### From Previous Version

If you're upgrading from a version without configurable compression:

1. **Add environment variables** to your `.env` files
2. **Restart your server** to pick up new configuration
3. **Test your API calls** to ensure everything works as expected
4. **Monitor logs** to verify compression behavior

### Recommended Settings

**Development:**
```bash
ENABLE_COMPRESSION=false  # Easier debugging
```

**Staging:**
```bash
ENABLE_COMPRESSION=true
COMPRESSION_THRESHOLD=5120  # Test with production-like settings
```

**Production:**
```bash
ENABLE_COMPRESSION=true
ENABLE_REQUEST_COMPRESSION=true
ENABLE_RESPONSE_COMPRESSION=true
COMPRESSION_THRESHOLD=5120  # Optimize for bandwidth
```

## Troubleshooting

### Issue: Compression not working
**Check:**
1. Environment variables are set correctly
2. Server was restarted after changing variables
3. Data size exceeds the threshold
4. Debug logs show compression activity

### Issue: Performance problems
**Solutions:**
1. Increase `COMPRESSION_THRESHOLD` to compress only larger payloads
2. Disable compression for development: `ENABLE_COMPRESSION=false`
3. Use response-only compression: `ENABLE_REQUEST_COMPRESSION=false`

### Issue: Debugging difficulties
**Solutions:**
1. Temporarily disable compression: `ENABLE_COMPRESSION=false`
2. Enable debug logging: `LOG_LEVEL=debug`
3. Check compression logs in both client and server

## Best Practices

1. **Use compression in production** for bandwidth savings
2. **Disable compression in development** for easier debugging
3. **Set appropriate thresholds** based on your typical payload sizes
4. **Monitor compression ratios** to ensure effectiveness
5. **Test thoroughly** when changing compression settings
6. **Document your configuration** for team members

This configuration system provides fine-grained control over compression behavior while maintaining backward compatibility and ease of use.
