import { Router } from 'express';
import qnaRoutes from './qna/qna.routes';
import examRoutes from './exam/exam.routes';
import bookmarkRoutes from './bookmark/bookmark.routes';
import aiRoutes from './ai/ai.router';
import purchaseRoutes from './purchase/purchase.routes';
import imageRoutes from './image/image.routes';
import authRoutes from './auth/auth.routes';
import userRoutes from './user/user.routes';

const router = Router();

// Authentication routes
router.use('/auth', authRoutes);

// User routes
router.use('/user', userRoutes);

// Existing routes
router.use('/qna', qnaRoutes);
router.use('/exam', examRoutes);
router.use('/bookmark', bookmarkRoutes);
router.use('/ai', aiRoutes);
router.use('/purchase', purchaseRoutes);
router.use('/image', imageRoutes);

export default router;