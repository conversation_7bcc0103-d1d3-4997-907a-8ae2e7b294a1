import { UserService } from '../user/user.service';
import axios from 'axios';
import { logger } from '../../utils/logger';
import { googleTokenService } from '../../services/googleToken.service';
import * as dotenvx from '@dotenvx/dotenvx';

export class AuthService {
  private userService: UserService;

  constructor() {
    this.userService = new UserService();
  }

  /**
   * Verify Google ID token and authenticate user
   */
  async loginWithGoogle(idToken: string, deviceInfo?: any): Promise<any> {
    logger.info('AuthService.loginWithGoogle called', {
      hasIdToken: !!idToken,
      idTokenLength: idToken ? idToken.length : 0,
      hasDeviceInfo: !!deviceInfo
    });

    try {
      // In a real implementation, you would verify the token with Google
      // For now, we'll simulate the verification

      // Simulate Google token verification
      // In production, you would use the Google OAuth2 API
      let googleUserInfo;

      logger.debug('Checking token type and environment');

      const testToken = dotenvx.get('TEST_TOKEN') || 'test-token';
      if (dotenvx.get('MODE') !== 'production' && idToken === testToken) {
        logger.info('Using test token for development');
        // For testing purposes
        googleUserInfo = {
          sub: 'test-google-id',
          email: '<EMAIL>',
          name: 'Test User',
          picture: 'https://example.com/test-profile.jpg'
        };
      } else {
        logger.info('Verifying Google token with Google API');
        try {
          // This is a simplified version - in production, use Google's OAuth2 libraries
          const response = await axios.get(
            `https://oauth2.googleapis.com/tokeninfo?id_token=${idToken}`
          );
          googleUserInfo = response.data;
          logger.info('Google token verification successful', {
            sub: googleUserInfo.sub,
            email: googleUserInfo.email
          });
        } catch (error) {
          logger.error({ error }, 'Error verifying Google token');
          return { success: false, message: 'Invalid Google token' };
        }
      }

      logger.debug('Google user info obtained:', {
        sub: googleUserInfo.sub,
        email: googleUserInfo.email,
        name: googleUserInfo.name
      });

      // Find or create user
      logger.info('Looking for existing user by Google ID');
      let user = await this.userService.findUserByGoogleId(googleUserInfo.sub);

      if (!user) {
        logger.info('User not found, creating new user');
        // Create new user
        user = await this.userService.createUserWithGoogle(
          googleUserInfo.sub,
          googleUserInfo.email,
          googleUserInfo.name,
          googleUserInfo.picture
        );
        logger.info('New user created', { userId: user._id });
      } else {
        logger.info('Existing user found', { userId: user._id });
      }

      // Record login
      logger.debug('Recording login');
      await this.userService.recordLogin(user._id?.toString() || user.id, deviceInfo);

      // Store the Google token and create user mapping
      logger.info('Storing Google token for user session');
      const userId = user._id?.toString() || user.id;
      await googleTokenService.storeTokenUserMapping(idToken, userId, googleUserInfo.sub);

      logger.info('Google token stored successfully', {
        tokenLength: idToken ? idToken.length : 0,
        tokenPreview: idToken ? `${idToken.substring(0, 10)}...${idToken.substring(idToken.length - 5)}` : 'none',
        userId: userId
      });

      // Retrieve the complete user object from MongoDB
      logger.debug('Retrieving complete user object from database');
      const completeUser = await this.userService.getUserByGoogleId(googleUserInfo.sub);

      if (!completeUser) {
        logger.error('Failed to retrieve complete user object after successful authentication', {
          googleId: googleUserInfo.sub,
          userId: userId
        });
        throw new Error('Failed to retrieve user data');
      }

      logger.debug('Complete user object retrieved successfully', {
        userId: completeUser._id,
        hasProgress: !!completeUser.progress,
        quizResultsCount: completeUser.quizResults?.length || 0,
        purchasesCount: completeUser.purchases?.length || 0,
        aiChatsCount: completeUser.aiChats?.length || 0,
        loginHistoryCount: completeUser.loginHistory?.length || 0
      });

      const result = {
        success: true,
        token: idToken, // Return the Google ID token as the authentication token
        user: {
          _id: completeUser._id,
          googleId: completeUser.googleId,
          email: completeUser.email,
          displayName: completeUser.displayName,
          profilePicture: completeUser.profilePicture,
          createdAt: completeUser.createdAt,
          updatedAt: completeUser.updatedAt,
          progress: completeUser.progress,
          quizResults: completeUser.quizResults,
          purchases: completeUser.purchases,
          aiCredits: completeUser.aiCredits,
          aiChats: completeUser.aiChats,
          loginHistory: completeUser.loginHistory
        }
      };

      logger.info('AuthService.loginWithGoogle completed successfully', {
        success: result.success,
        hasToken: !!result.token,
        tokenLength: result.token ? result.token.length : 0,
        userId: completeUser._id?.toString() || userId
      });

      return result;
    } catch (error) {
      logger.error({ error }, 'Error in loginWithGoogle');
      throw error;
    }
  }

  /**
   * Logout user and invalidate Google token
   */
  async logout(token: string): Promise<{ success: boolean }> {
    try {
      logger.info('Logging out user and invalidating token');

      // Invalidate the Google token from cache
      await googleTokenService.invalidateToken(token);

      logger.info('User logged out successfully');
      return { success: true };
    } catch (error) {
      logger.error('Error during logout', { error });
      throw error;
    }
  }

  /**
   * Validate Google token
   */
  async validateGoogleToken(token: string): Promise<{ success: boolean; userId?: string; googleId?: string; message?: string }> {
    try {
      const validation = await googleTokenService.validateToken(token);

      if (!validation.isValid) {
        return {
          success: false,
          message: validation.error || 'Invalid token'
        };
      }

      // For test tokens
      if (validation.userId === 'test-user-id') {
        return {
          success: true,
          userId: 'test-user-id',
          googleId: 'test-google-id'
        };
      }

      // For real tokens, find user by Google ID
      if (validation.googleId) {
        const user = await this.userService.findUserByGoogleId(validation.googleId);
        if (user) {
          return {
            success: true,
            userId: user._id?.toString() || user.id,
            googleId: validation.googleId
          };
        } else {
          return {
            success: false,
            message: 'User not found'
          };
        }
      }

      return {
        success: false,
        message: 'Invalid token data'
      };
    } catch (error) {
      logger.error({ error }, 'Error validating Google token');
      return {
        success: false,
        message: 'Token validation failed'
      };
    }
  }
}