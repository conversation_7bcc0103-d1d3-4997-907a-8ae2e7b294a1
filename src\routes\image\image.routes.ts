import { Router } from 'express';
import path from 'path';

const router = Router();


/**
 * @swagger
 * /api/image/{filename}:
 *   get:
 *     summary: Fetch an image by filename
 *     tags: [Image]
 *     parameters:
 *       - in: path
 *         name: filename
 *         required: true
 *         schema:
 *           type: string
 *         description: Name of the image file
 *     responses:
 *       200:
 *         description: Image fetched successfully
 *         content:
 *           image/png:
 *             schema:
 *               type: string
 *               format: binary
 *       404:
 *         description: Image not found
 */
router.get('/:filename', (req, res) => {
    let { filename } = req.params;
    
    filename = filename.replace(/^images\//, '');

    const imagePath = path.join(__dirname, '../../public/images', filename);

    res.sendFile(imagePath, (err) => {
        if (err) {
            res.status(404).json({ success: false, message: 'Image not found' });
        }
    });
});

export default router;