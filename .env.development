MODE=development
STRIPE_ACCOUNT_ID=acct_1ETX7UKltkwsDdsB
STRIPE_SECRET_KEY=sk_test_L4fywSA9mcuT1J8raQbt15DG00SHdgUtme
WP_ENDPOINT=https://hkit.supply/
MONGODB_URI=mongodb+srv://new_pc:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0
WOO_CONSUMER_KEY=ck_1dcc0b14fe78daa5c381fe0a5b682ba0f26babdc
WOO_CONSUMER_SECRET=cs_cec3a0aa45f022c9e298f4179680d2ed861c640e
CORS_ORIGIN=*
PORT=3016
API_URL=http://localhost:3016

# Google Authentication Configuration
GOOGLE_CLIENT_ID=your-google-client-id.apps.googleusercontent.com
GOOGLE_TOKEN_INFO_URL=https://oauth2.googleapis.com/tokeninfo
GOOGLE_TOKEN_CACHE_TTL=3600
USE_GOOGLE_AUTH=true
USE_JWT_AUTH=false
TEST_TOKEN=test-token
USER_DB_COLLECTION=aws
# Enable debug logging for authentication
LOG_LEVEL=debug

# Compression Configuration
ENABLE_COMPRESSION=false
ENABLE_REQUEST_COMPRESSION=false
ENABLE_RESPONSE_COMPRESSION=false
COMPRESSION_THRESHOLD=5120

QNA_VENDOR='amazon'

# Email Configuration for Feedback Reports
GMAIL_USER="<EMAIL>"
GMAIL_PASSWORD="vpvf hgkk jwmp tnly"
GMAIL_HOST="smtp.gmail.com"
GMAIL_PORT=587
GMAIL_SECURE=false

# Token Validation Configuration
DISABLE_TOKEN_VALIDATION=true