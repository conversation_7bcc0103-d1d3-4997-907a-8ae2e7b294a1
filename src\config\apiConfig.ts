
import * as dotenvx from '@dotenvx/dotenvx';

export const apiConfig = {
    baseDataUrl: dotenvx.get('MONGODB_URI') as string,
    authServiceUrl: dotenvx.get('WP_ENDPOINT') + 'wp-json/base/v1/verify_token',
    wooEndpoint: dotenvx.get('WP_ENDPOINT') + 'wp-json/wc/v3',
    aiToken: "sk-lmab8Wl6AVU3VLKjsoGTP4H3zEK1CSV9HE1H7W7eOq3HXFPi",
    aiEndpoint: "https://api.chatanywhere.org/v1/chat/completions",
    formatDateTime: {
        defaultDate: 'YYYY/MM/DD',
        defaultDateTime: 'YYYY/MM/DD HH:mm:ss'
    },
    databaseName: {
        qna: 'qna',
        exam: 'exam',
        user: 'user',
        payment: 'payment',
    },
    collectionName: {
        user: {
            ai_chat: 'ai_chat',
            bookmark: 'bookmark',
        }
    },
    code: {
        success: 200,
        error: 400,
        unauthorized: 401,
        tokenExpired: 402,
        notFound: 404,
        badRequest: 400,
        orderNotCompleted: 1005,
        orderAlreadyCreated: 1006
    },
    codeMessage: {
        success: 'Success',
        error: 'Error',
        unauthorized: 'Unauthorized - Invalid token',
        tokenExpired: 'Unauthorized - Token expired',
        notFound: 'Not Found',
        badRequest: 'Bad Request',
        orderNotCompleted: 'Order is still processing',
        orderAlreadyCreated: 'Order already created'
    },
    qna: {
        endpoint: '/qna'
    }
}