
import { Request, Response } from 'express';
import axios from 'axios';
import fs from 'fs';
import path from 'path';
import crypto from 'crypto';
import { AIService } from './ai.service';
import { apiConfig } from '../../config/apiConfig';
import { parseAiResponse } from '../../utils/appHelper';
import { logger } from '../../utils/logger';
import { QnAService } from '../qna/qna.service';
import { countTotalTokens, truncateMessages } from '../../utils/tokenCounter';
// Import the getModel function from the QnAService
// This avoids having to import directly from dbHelper

// Helper function to mask sensitive information
const maskSensitiveInfo = (obj: any): any => {
  if (!obj) return obj;

  // Create a deep copy to avoid modifying the original object
  const maskedObj = JSON.parse(JSON.stringify(obj));

  // Mask API keys in headers
  if (maskedObj.headers && maskedObj.headers.Authorization) {
    const authHeader = maskedObj.headers.Authorization;
    if (typeof authHeader === 'string' && authHeader.startsWith('Bearer ')) {
      const apiKey = authHeader.substring(7);
      const maskedKey = apiKey.substring(0, 4) + '****' + apiKey.substring(apiKey.length - 4);
      maskedObj.headers.Authorization = `Bearer ${maskedKey}`;
    }
  }

  return maskedObj;
};

// Log client requests to file
const logRequestToFile = (data: any) => {
  if (process.env.NODE_ENV !== 'development') return;

  const logDir = path.join(__dirname, '../../logs');
  const logFile = path.join(logDir, 'ai_requests.log');

  if (!fs.existsSync(logDir)) {
    fs.mkdirSync(logDir, { recursive: true });
  }

  const logEntry = `${new Date().toISOString()} - ${JSON.stringify(data)}\n`;
  fs.appendFileSync(logFile, logEntry);
};

// Log AI API interactions to file
const logAIApiInteraction = (requestId: string, type: 'request' | 'response', data: any) => {
  if (process.env.NODE_ENV !== 'development') return;

  const logDir = path.join(__dirname, '../../logs');
  const logFile = path.join(logDir, 'ai_api_logs.log');

  if (!fs.existsSync(logDir)) {
    fs.mkdirSync(logDir, { recursive: true });
  }

  // Mask sensitive information
  const maskedData = maskSensitiveInfo(data);

  const logEntry = {
    timestamp: new Date().toISOString(),
    requestId,
    type,
    data: maskedData
  };

  fs.appendFileSync(logFile, JSON.stringify(logEntry) + '\n');
};


interface AuthenticatedRequest extends Request {
  user_id?: string;
}

export const askAI = async (req: AuthenticatedRequest, res: Response) => {
  const userId = Number(req.user_id);
  const { question, type, code, qna, previous_conversations } = req.body; // type: 1: initial, 2 sequence
  const answer = await AIService.ask(question, type, previous_conversations);
  let response: any = {};
  let answerText = "";
  if (type == 1) {
    if (answer && answer.choices && answer.choices.length > 0) {
      answerText = answer.choices[0].message.content;
      response = parseAiResponse(answerText);
    } else {
      response = answer;
    }
  } else {
    if (answer && answer.choices && answer.choices.length > 0) {
      answerText = answer.choices[0].message.content;
      response = parseAiResponse(answerText, 2);
    } else {
      response = answer;
    }
  }
  await AIService.saveAIChatHistory(userId || 0, code, qna, question, answerText);

  try {
    res.json({
      success: true,
      code: apiConfig.code.success,
      data: response
    });
  } catch (error) {
    console.error('Error in AI response:', error);
    res.status(500).json({
      success: false,
      code: apiConfig.code.error,
      error: 'Failed to get AI response'
    });
  }
};

export const getAIChats = async (req: AuthenticatedRequest, res: Response) => {
  const userId = Number(req.user_id);
  const { code, qna } = req.body;
  const history = await AIService.getAIChats(userId || 0, code, qna);
  console.log("history: " + history);
  if (history) {
    const response: any = [];
    for (let i = 0; i < history.history.length; i++) {
      const item = history.history[i];
      if (i == 0) {
        response.push({
          question: item.question,
          ...parseAiResponse(item.response)
        });
      } else {
        response.push({
          question: item.question,
          ...parseAiResponse(item.response, 2)
        });
      }
    }
    res.json({
      success: true,
      code: apiConfig.code.success,
      data: response
    });
  } else {
    res.json({
      success: false,
      code: apiConfig.code.error,
      error: 'No history found'
    });
  }
}

export const saveAIChatHistory = async (
  req: AuthenticatedRequest,
  res: Response
) => {
  try {
    const userId = Number(req.user_id);
    logger.debug({ userId }, "userId in controller");
    const { code, qna, question, answer } = req.body;

    // Basic validation
    if (!code || !qna || !question || !answer) {
      res.status(400).json({
        success: false,
        code: 400,
        error: 'Missing required fields'
      });
    }

    await AIService.saveAIChatHistory(
      userId,
      code,
      qna,
      question,
      answer
    );

    try {
      res.json({
        success: true,
        code: 200,
        data: { message: "History saved successfully" }
      });
    } catch (error) {
      console.error('Error in AI response:', error);
      res.status(500).json({
        success: false,
        code: apiConfig.code.error,
        error: 'Failed to get AI response'
      });
    }

  } catch (error) {
    // Error handling
  }
};

export const askAISimple = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  // Generate a unique request ID for tracking this API interaction
  const requestId = crypto.randomBytes(16).toString('hex');

  try {
    // Log complete request details to file
    logRequestToFile({
      timestamp: new Date().toISOString(),
      requestId,
      endpoint: 'askAISimple',
      method: req.method,
      url: req.url,
      headers: req.headers,
      body: req.body,
      query: req.query,
      params: req.params,
      user: req.user_id
    });

    // Extract data from request body
    const { question, chatHistory, questionId } = req.body;

    if (!question) {
      res.status(400).json({
        success: false,
        code: apiConfig.code.badRequest,
        error: 'Question is required'
      });
      return;
    }

    // Return dummy response in development mode
    /* if (process.env.MODE === 'development') {
      // Log dummy response for consistency
      logAIApiInteraction(requestId, 'request', {
        endpoint: apiConfig.aiEndpoint,
        method: 'POST',
        model: 'gpt-4.1-mini',
        messages: [
          {
            role: 'user',
            content: question + " Please suggest your answers, beginning with **Answers:**. Then suggest 3 follow-up questions for users to ask in point form concisely that help user to understand the relevant concept more. Beginning with **Follow-up Questions:**, each new line represents a new follow-up question."
          }
        ],
        isDevelopmentMode: true
      });

      const dummyResponse = {
        answer: "The sentence **\"Einstein invented the light bulb\"** is false. \n**Explanation:** The light bulb was invented by Thomas Edison in the late **19th** century, although other inventors, like Joseph Swan, also contributed to its development.",
        follow_up_questions: [
          "123What were some of the key inventions attributed to Albert Einstein?",
          "435How did Thomas Edison's work on the light bulb influence modern electrical systems?",
          "627Can you explain the differences between Edison's and Swan's designs for the light bulb?"
        ].filter(q => q.trim() !== "") // Filter out empty strings
      };

      logAIApiInteraction(requestId, 'response', {
        isDevelopmentMode: true,
        data: dummyResponse
      });

      res.json({
        success: true,
        code: apiConfig.code.success,
        data: dummyResponse
      });
      return;
    } */

    // Process the chat history and extract questionID if available
    let questionID = null;
    let messages = [];
    let originalSystemMessage = null;

    // First, check if there's a top-level questionId field in the request body
    if (questionId) {
      questionID = questionId;
      logger.debug(`[${requestId}] Found questionID in top-level questionId field: ${questionID}`);
    }

    // If no questionID found yet, try to extract it from chat history
    if (!questionID && chatHistory && Array.isArray(chatHistory) && chatHistory.length > 0) {
      logger.debug(`[${requestId}] Chat history received with ${chatHistory.length} messages`);

      // Extract questionID from the system message (which should be the last message in reverse chronological order)
      for (let i = chatHistory.length - 1; i >= 0; i--) {
        const message = chatHistory[i];
        if (message.role === 'system' && message.content) {
          logger.debug(`[${requestId}] Found system message: ${message.content}`);
          originalSystemMessage = message.content;

          // Try to extract URL from the system message using multiple patterns
          // First try the standard URL pattern
          let urlMatch = /https?:\/\/[^\s]+/g.exec(message.content);

          // If that doesn't work, try to find anything that looks like a question ID
          if (!urlMatch) {
            urlMatch = /questionID[=:]\s*["']?([^"'\s]+)["']?/i.exec(message.content);
            if (urlMatch) {
              questionID = urlMatch[1]; // Use the captured group
            }
          } else {
            questionID = urlMatch[0]; // Use the full URL
          }

          // If still no match, try to extract any alphanumeric ID that might be a question ID
          if (!urlMatch) {
            urlMatch = /ID[=:]\s*["']?([a-zA-Z0-9_-]+)["']?/i.exec(message.content);
            if (urlMatch) {
              questionID = urlMatch[1];
            }
          }

          if (questionID) {
            logger.debug(`[${requestId}] Extracted questionID from system message: ${questionID}`);
            break;
          } else {
            logger.debug(`[${requestId}] Could not extract questionID from system message`);
          }
        }
      }
    } else if (!questionID) {
      logger.debug(`[${requestId}] No chat history received and no questionId in request body`);
    }

    // Process chat history in chronological order (reverse the array since it's in reverse chronological order)
    if (chatHistory && Array.isArray(chatHistory) && chatHistory.length > 0) {
      // Sort chat history by timestamp if available
      const chronologicalHistory = [...chatHistory].sort((a, b) => {
        // If timestamps are available, use them for sorting
        if (a.timestamp && b.timestamp) {
          return parseInt(a.timestamp) - parseInt(b.timestamp);
        }
        // Otherwise, assume the array is already in reverse chronological order
        return 0;
      });

      logger.debug(`[${requestId}] Processed chat history in chronological order with ${chronologicalHistory.length} messages`);

      // Add all messages to the messages array (we'll handle system messages later)
      for (const message of chronologicalHistory) {
        if (message.role && message.content) {
          // Skip system messages for now - we'll add our own system message later
          if (message.role !== 'system') {
            messages.push({
              role: message.role,
              content: message.content
            });
            logger.debug(`[${requestId}] Added ${message.role} message to messages array from chat history`);
          } else if (!originalSystemMessage) {
            // Save the system message if we don't have one yet
            originalSystemMessage = message.content;
          }
        }
      }
    } else {
      logger.debug(`[${requestId}] No chat history to process`);
    }

    // If we have a questionID, fetch the QnA document
    let qnaDocument = null;
    if (questionID) {
      try {
        logger.debug(`[${requestId}] Fetching QnA document for URL: ${questionID}`);

        // Try to get the document using the URL
        qnaDocument = await QnAService.getQnaByID(questionID);

        // If that fails, try other methods
        if (!qnaDocument) {
          logger.debug(`[${requestId}] Document not found by URL, trying alternative methods`);
          try {
            // Try to get the document using the QnAService methods
            const collections = await QnAService.getQnACollections();
            logger.debug(`[${requestId}] Available collections: ${collections ? collections.join(', ') : 'none'}`);

            if (collections && collections.length > 0) {
              // Try each collection
              for (const collectionName of collections) {
                try {
                  logger.debug(`[${requestId}] Trying to get QnA from collection: ${collectionName}`);
                  // Try to get the document by ID
                  const doc = await QnAService.getQnASentence(collectionName, questionID);
                  if (doc) {
                    qnaDocument = doc;
                    logger.debug(`[${requestId}] Found document in collection: ${collectionName}`);
                    break;
                  }
                } catch (collError: any) {
                  logger.debug(`[${requestId}] Error getting QnA from collection ${collectionName}: ${collError.message || 'Unknown error'}`);
                }
              }
            }
          } catch (innerError) {
            logger.error(`[${requestId}] Error trying alternative lookup methods:`, innerError);
          }
        }

        logger.debug(`[${requestId}] QnA document retrieved: ${qnaDocument ? 'Yes' : 'No'}`);
        if (qnaDocument) {
          logger.debug(`[${requestId}] QnA document content: ${JSON.stringify(qnaDocument)}`);
        } else {
          // If we still don't have a document, create a mock one from the original system message
          if (originalSystemMessage) {
            logger.debug(`[${requestId}] Creating mock QnA document from system message`);
            qnaDocument = {
              exam: {
                question: originalSystemMessage,
                choices: [],
                answer: "Please refer to the system message for context."
              }
            };
          }
        }
      } catch (error) {
        logger.error(`[${requestId}] Error fetching QnA document for URL ${questionID}:`, error);
      }
    } else {
      logger.debug(`[${requestId}] No questionID found in request body or chat history`);
    }

    // Prepare system context from QnA document if available
    let systemContext = '';

    // First, check if we have a QnA document
    if (qnaDocument) {
      logger.debug(`[${requestId}] Processing QnA document for system context`);

      // Check if the document has an exam property
      if (!qnaDocument.exam) {
        logger.error(`[${requestId}] QnA document does not contain exam data`);

        // Try to find exam data in other properties
        const anyDoc = qnaDocument as any; // Cast to any to access potential properties
        if (anyDoc.question || anyDoc.choices || anyDoc.answer) {
          logger.debug(`[${requestId}] Found question data at root level of document`);
          qnaDocument.exam = {
            question: anyDoc.question || '',
            choices: anyDoc.choices || [],
            answer: anyDoc.answer || ''
          };
        } else {
          // Create a basic exam object from the original system message if available
          if (originalSystemMessage) {
            logger.debug(`[${requestId}] Creating exam object from system message`);
            qnaDocument.exam = {
              question: originalSystemMessage,
              choices: [],
              answer: "Please refer to the system message for context."
            };
          } else {
            logger.debug(`[${requestId}] No exam data and no system message available`);
          }
        }
      }

      // Now process the exam data if available
      if (qnaDocument.exam) {
        const { exam } = qnaDocument;
        logger.debug(`[${requestId}] Processing exam data: ${JSON.stringify(exam)}`);

        // Format choices as a string
        let choicesText = '';
        if (exam.choices && Array.isArray(exam.choices)) {
          exam.choices.forEach((choice: any, index: number) => {
            const letter = String.fromCharCode(65 + index); // A, B, C, D...
            let choiceText = '';

            // Handle different choice formats
            if (typeof choice === 'string') {
              choiceText = choice;
            } else if (choice[letter]) {
              choiceText = choice[letter];
            } else if (choice.text) {
              choiceText = choice.text;
            } else {
              // Try to extract any property that might contain the choice text
              const keys = Object.keys(choice);
              if (keys.length > 0) {
                choiceText = choice[keys[0]];
              }
            }

            choicesText += `${letter}. ${choiceText}\n`;
          });
          logger.debug(`[${requestId}] Formatted choices: ${choicesText}`);
        } else {
          logger.debug(`[${requestId}] Exam choices are missing or not in expected format`);
        }

        // Format correct answers
        let correctAnswers = '';
        if (exam.answer) {
          if (Array.isArray(exam.answer)) {
            correctAnswers = exam.answer.join(', ');
          } else if (typeof exam.answer === 'string') {
            correctAnswers = exam.answer;
          } else if (typeof exam.answer === 'object') {
            // Try to extract the answer from the object
            correctAnswers = JSON.stringify(exam.answer);
          }
          logger.debug(`[${requestId}] Formatted correct answers: ${correctAnswers}`);
        } else {
          logger.debug(`[${requestId}] Correct answers are missing`);
        }

        // Create system context with detailed instructions
        systemContext = `
You are an AI assistant helping with exam questions. Please analyze the following question and provide a detailed explanation:

Question: ${exam.question || 'No question provided'}

${choicesText ? `Choices:
${choicesText}` : ''}

${correctAnswers ? `Correct Answer: ${correctAnswers}` : ''}

IMPORTANT: DO NOT mention specific choice letters (A, B, C, D) in your explanations. Answer choices may be shuffled for different users, so referring to choices by their letters could be misleading. Instead, focus on explaining the content of the correct answer and why it's right, and describe why incorrect options are wrong based on their content rather than their letter designation.`;

        logger.debug(`[${requestId}] Created system context: ${systemContext}`);
      }
    } else if (originalSystemMessage) {
      // If we don't have a QnA document but we do have a system message, use that
      logger.debug(`[${requestId}] No QnA document available, using original system message`);
      systemContext = originalSystemMessage;
    } else {
      logger.debug(`[${requestId}] No QnA document or system message available, skipping system context`);
    }

    // Prepare final messages array
    const finalMessages = [];

    // Add system context if available
    if (systemContext && systemContext.trim() !== '') {
      finalMessages.push({
        role: 'system',
        content: systemContext
      });
      logger.debug(`[${requestId}] Added system context to messages array`);
    } else {
      logger.debug(`[${requestId}] No system context to add`);
    }

    // Add all previous messages from chat history
    if (messages.length > 0) {
      finalMessages.push(...messages);
      logger.debug(`[${requestId}] Added ${messages.length} messages from chat history`);
    }

    // Add the current question to messages
    finalMessages.push({
      role: 'user',
      content: question + ". Suggest your answers beginning with **Answers:**. Then suggest 3 follow-up questions for users to ask in point form concisely that help user to understand the relevant concept more. Beginning with **Follow-up Questions:**, each new line represents a new follow-up question."
    });

    // Count tokens and truncate if necessary
    const totalTokens = countTotalTokens(finalMessages);
    logger.debug(`[${requestId}] Total tokens in messages: ${totalTokens}`);

    // Truncate messages if they exceed the token limit (4000 for gpt-4.1-mini)
    const MAX_TOKENS = 4000;
    const BUFFER_TOKENS = 100; // Leave a 100-token buffer as specified in requirements
    let messagesToSend = finalMessages;

    if (totalTokens > MAX_TOKENS - BUFFER_TOKENS) {
      logger.debug(`[${requestId}] Messages exceed token limit (${totalTokens} > ${MAX_TOKENS - BUFFER_TOKENS}), truncating...`);

      // Use our enhanced truncation function with priority-based approach
      messagesToSend = truncateMessages(finalMessages, MAX_TOKENS, BUFFER_TOKENS);

      // Log detailed information about the truncation
      logger.debug(`[${requestId}] Truncated messages from ${finalMessages.length} to ${messagesToSend.length}`);
      logger.debug(`[${requestId}] System messages preserved: ${messagesToSend.filter(m => m.role === 'system').length}`);
      logger.debug(`[${requestId}] User messages preserved: ${messagesToSend.filter(m => m.role === 'user').length}`);
      logger.debug(`[${requestId}] Assistant messages preserved: ${messagesToSend.filter(m => m.role === 'assistant').length}`);

      // Log the final token count after truncation
      const finalTokenCount = countTotalTokens(messagesToSend);
      logger.debug(`[${requestId}] Final token count after truncation: ${finalTokenCount}/${MAX_TOKENS - BUFFER_TOKENS}`);
    }

    // Log the messages array for debugging
    logger.debug(`[${requestId}] Messages array before sending to API: ${JSON.stringify(messagesToSend)}`);
    logger.debug("apiConfig.aiEndpoint: " + apiConfig.aiEndpoint);

    // Prepare request configuration
    const requestConfig = {
      url: apiConfig.aiEndpoint,
      method: 'POST',
      data: {
        model: "gpt-4.1-mini",
        messages: messagesToSend,
        temperature: 0.7
      },
      headers: {
        'Authorization': `Bearer ${apiConfig.aiToken}`,
        'Content-Type': 'application/json'
      }
    };

    // Log the request to the AI API
    logAIApiInteraction(requestId, 'request', requestConfig);

    // Make the API request
    const response = await axios.post(
      apiConfig.aiEndpoint,
      requestConfig.data,
      {
        headers: requestConfig.headers
      }
    );

    // Log the response from the AI API
    logAIApiInteraction(requestId, 'response', response.data);

    // Extract and format the AI response
    let formattedResponse: {
      answer: string;
      follow_up_questions: string[];
    } = {
      answer: "",
      follow_up_questions: []
    };

    if (response.data && response.data.choices && response.data.choices.length > 0) {
      const aiResponse = response.data.choices[0].message.content;

      // Parse the response using regex
      const answersMatch = /\*\*Answers:\*\*([\s\S]*?)(?=\*\*Follow-up Questions:\*\*|$)/i.exec(aiResponse);
      const followUpMatch = /\*\*Follow-up Questions:\*\*([\s\S]*)/i.exec(aiResponse);

      // Extract answers and follow-up questions
      formattedResponse.answer = answersMatch ? answersMatch[1].trim() : aiResponse;

      // Process follow-up questions
      if (followUpMatch) {
        // Since the format specifies each new line represents a new follow-up question
        formattedResponse.follow_up_questions = followUpMatch[1]
          .split(/\n+/)
          .map(q => q.replace(/^\d+\.\s*/, '').replace(/^-\s*/, '').trim())
          .filter(q => q !== ""); // Filter out empty strings
      }
    }

    // Final filter to ensure no empty strings in follow-up questions
    formattedResponse.follow_up_questions = formattedResponse.follow_up_questions.filter(q => q.trim() !== "");

    // Log the formatted response
    logger.debug(`[${requestId}] Formatted response: ${JSON.stringify(formattedResponse)}`);

    // Return the formatted response
    res.json({
      success: true,
      code: apiConfig.code.success,
      data: formattedResponse
    });
  } catch (error) {
    // Log the error with the request ID for correlation
    logger.error(`[${requestId}] Error in askAISimple:`, error);

    // Log the error to the AI API log file
    logAIApiInteraction(requestId, 'response', {
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined
    });

    res.status(500).json({
      success: false,
      code: apiConfig.code.error,
      error: 'Failed to get AI response'
    });
  }
};