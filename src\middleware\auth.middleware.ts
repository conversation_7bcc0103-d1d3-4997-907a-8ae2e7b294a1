import { Request, Response, NextFunction } from 'express';
import { apiConfig } from '../config/apiConfig';
import { logger } from '../utils/logger';
import { googleTokenService } from '../services/googleToken.service';

interface AuthenticatedRequest extends Request {
  user_id?: string;
  google_id?: string;
}

export const authenticateToken = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
) => {
  const requestId = `auth-${Date.now()}`;

  // Check if token validation is disabled
  if (process.env.DISABLE_TOKEN_VALIDATION === 'true') {
    logger.debug(`[${requestId}] ⚠️ Token validation is disabled - skipping authentication`);
    req.user_id = 'disabled-token-validation';
    req.google_id = 'disabled-token-validation';
    return next();
  }

  // Enhanced initial logging with request context
  logger.debug(`[${requestId}] 🔐 Starting Google token authentication`, {
    method: req.method,
    url: req.originalUrl,
    userAgent: req.headers['user-agent'],
    contentType: req.headers['content-type'],
    hasAuthHeader: !!req.headers.authorization,
    authHeaderFormat: req.headers.authorization ? req.headers.authorization.substring(0, 20) + '...' : 'none'
  });

  try {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];
    logger.debug(`[${requestId}] 🔍 Auth header analysis`, {
      hasAuthHeader: !!authHeader,
      hasToken: !!token,
      tokenLength: token ? token.length : 0,
      authHeaderPreview: authHeader ? `${authHeader.substring(0, 20)}...` : 'none'
    });

    if (!token) {
      logger.warn(`[${requestId}] 🚫 No token provided in Authorization header`, {
        authHeaderExists: !!authHeader,
        authHeaderValue: authHeader ? `${authHeader.substring(0, 20)}...` : 'none',
        requestMethod: req.method,
        requestUrl: req.originalUrl,
        troubleshooting: [
          'Ensure Authorization header is included in request',
          'Verify header format: "Authorization: Bearer <token>"',
          'Check if token is being sent from client application'
        ]
      });
      res.status(apiConfig.code.unauthorized).json({
        success: false,
        code: apiConfig.code.unauthorized,
        error: 'No authentication token provided',
        message: 'Please provide a valid authentication token',
        data: null
      });
      return;
    }

    // Enhanced token analysis for debugging
    logger.debug(`[${requestId}] 🔍 Token format analysis`, {
      tokenLength: token.length,
      tokenPreview: `${token.substring(0, 20)}...${token.substring(token.length - 10)}`,
      tokenFormat: {
        startsWithEyJ: token.startsWith('eyJ'),
        containsDots: (token.match(/\./g) || []).length,
        isJWTFormat: token.split('.').length === 3,
        hasValidJWTStructure: token.split('.').length === 3 && token.startsWith('eyJ')
      },
      isTestToken: token === 'test-token',
      requestContext: {
        method: req.method,
        url: req.originalUrl
      }
    });

    // First check if we have a cached token-user mapping
    const cachedMapping = await googleTokenService.getUserFromToken(token);
    if (cachedMapping) {
      logger.debug(`[${requestId}] Using cached token-user mapping`, {
        userId: cachedMapping.userId,
        googleId: cachedMapping.googleId
      });
      req.user_id = cachedMapping.userId;
      req.google_id = cachedMapping.googleId;
      next();
      return;
    }

    // Validate token with Google
    logger.debug(`[${requestId}] Validating token with Google`);
    const validation = await googleTokenService.validateToken(token);

    if (!validation.isValid) {
      // Enhanced validation failure logging
      logger.warn(`[${requestId}] 🚫 Token validation failed - Detailed breakdown`, {
        validationError: validation.error,
        tokenLength: token ? token.length : 0,
        tokenPreview: token ? `${token.substring(0, 15)}...${token.substring(token.length - 5)}` : 'none',
        tokenType: token?.startsWith('eyJ') ? 'JWT-like' : 'other',
        requestMethod: req.method,
        requestUrl: req.originalUrl,
        userAgent: req.headers['user-agent']?.substring(0, 50),
        clientIP: req.ip || req.socket?.remoteAddress || 'unknown',
        timestamp: new Date().toISOString()
      });

      // Log specific troubleshooting steps based on error type
      if (validation.error === 'Token expired') {
        logger.warn(`[${requestId}] 🕐 Token expiration detected`, {
          troubleshooting: [
            'Client should refresh the Google ID token',
            'Check if token refresh is implemented in the app',
            'Verify token was not cached too long on client side'
          ]
        });
      } else if (validation.error === 'Invalid token format') {
        logger.warn(`[${requestId}] 📝 Invalid token format detected`, {
          troubleshooting: [
            'Verify client is sending Google ID token, not access token',
            'Check if token was corrupted during transmission',
            'Ensure proper Authorization header format: "Bearer <token>"'
          ]
        });
      } else if (validation.error === 'Token verification failed') {
        logger.warn(`[${requestId}] 🔒 Token verification failed`, {
          troubleshooting: [
            'Token may be issued for different Google Client ID',
            'Token signature verification failed',
            'Token may have been revoked by user'
          ]
        });
      } else {
        logger.warn(`[${requestId}] ❓ Unknown validation error`, {
          troubleshooting: [
            'Check network connectivity to Google services',
            'Verify Google token validation service is accessible',
            'Check server logs for more detailed error information'
          ]
        });
      }

      // Determine appropriate error code based on validation error
      let errorCode = apiConfig.code.unauthorized;
      if (validation.error === 'Token expired') {
        errorCode = apiConfig.code.tokenExpired;
      }

      res.status(errorCode).json({
        success: false,
        code: errorCode,
        error: validation.error || apiConfig.codeMessage.unauthorized,
        message: validation.message || (validation.error === 'Token expired' ? apiConfig.codeMessage.tokenExpired : apiConfig.codeMessage.unauthorized),
        data: null
      });
      return;
    }

    logger.debug(`[${requestId}] Token validation successful`, {
      googleId: validation.googleId,
      email: validation.email
    });

    // For test tokens, use predefined user ID
    if (validation.userId === 'test-user-id') {
      req.user_id = 'test-user-id';
      req.google_id = 'test-google-id';
      next();
      return;
    }

    // For real tokens, we need to find the user by Google ID
    req.google_id = validation.googleId;

    if (validation.googleId && validation.googleId !== 'test-google-id') {
      // Look up the actual user in MongoDB by Google ID
      logger.debug(`[${requestId}] Looking up user by Google ID`, { googleId: validation.googleId });

      try {
        // Import UserService dynamically to avoid circular dependencies
        const { UserService } = await import('../routes/user/user.service');
        const userService = new UserService();

        const user = await userService.findUserByGoogleId(validation.googleId);

        if (user) {
          // Use the MongoDB _id as the user_id
          const mongoUserId = user._id?.toString() || user.id;
          req.user_id = mongoUserId;

          // Store the correct token-user mapping with MongoDB _id
          await googleTokenService.storeTokenUserMapping(token, mongoUserId, validation.googleId);

          logger.debug(`[${requestId}] User found and mapped correctly`, {
            mongoUserId: mongoUserId,
            googleId: validation.googleId,
            email: user.email
          });
        } else {
          // User not found - this shouldn't happen if they went through proper login
          logger.warn(`[${requestId}] User not found in database for valid Google token`, {
            googleId: validation.googleId
          });

          res.status(apiConfig.code.unauthorized).json({
            success: false,
            code: apiConfig.code.unauthorized,
            error: 'User not found. Please log in again.',
            message: 'User not found. Please log in again.',
            data: null
          });
          return;
        }
      } catch (userLookupError) {
        logger.error(`[${requestId}] Error looking up user by Google ID`, {
          error: userLookupError instanceof Error ? userLookupError.message : String(userLookupError),
          googleId: validation.googleId
        });

        res.status(apiConfig.code.unauthorized).json({
          success: false,
          code: apiConfig.code.unauthorized,
          error: 'Authentication error. Please try again.',
          message: 'Authentication error. Please try again.',
          data: null
        });
        return;
      }
    }

    logger.debug(`[${requestId}] Authentication successful`, {
      userId: req.user_id,
      googleId: req.google_id
    });

    next();

  } catch (error) {
    logger.error(`[${requestId}] Authentication error`, { error: error instanceof Error ? error.message : String(error) });
    res.status(apiConfig.code.unauthorized).json({
      success: false,
      code: apiConfig.code.unauthorized,
      error: apiConfig.codeMessage.unauthorized,
      message: apiConfig.codeMessage.unauthorized,
      data: null
    });
    return;
  }
};