import { Schema, Document } from 'mongoose';

export interface Choice {
  [key: string]: string;
}

export interface Exam {
  question: string;
  choices: Choice[];
  answer: string[];
}

export interface IQnA extends Document {
  timestamp: number;
  first_sentence: string;
  exam: Exam;
  vote_distribution: {
    [key: string]: number;
  };
}

export const QnASchema = new Schema<IQnA>({
  timestamp: { type: Number, required: true },
  first_sentence: { type: String, required: true },
  exam: {
    question: { type: String, required: true },
    choices: [{ type: Map, of: String }],
    answer: [{ type: String, required: true }]
  },
  vote_distribution: { type: Map, of: Number, default: {} }
}, {
  timestamps: true,
  versionKey: false
});

// Add validations
QnASchema.pre('save', function(next) {
  if (!this.timestamp) {
    this.timestamp = Math.floor(Date.now() / 1000);
  }
  if (this.exam.choices.length === 0) {
    next(new Error('Choices cannot be empty'));
  }
  next();
});