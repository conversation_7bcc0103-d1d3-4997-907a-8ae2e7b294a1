import { Schema, Document } from 'mongoose';

export interface IExam extends Document {
  code: string;
  name: string;
  vendor: string;
  is_popular: boolean;
  date_updated: Date;
  total_questions: number;
  passing_score: number;
  time_limit: number;
}

export const ExamSchema = new Schema<IExam>({
  code: { type: String, required: true },
  name: { type: String, required: true },
  vendor: { type: String, required: true },
  is_popular: { type: Boolean, default: false },
  date_updated: { type: Date, required: true },
  total_questions: { type: Number, required: true },
  passing_score: { type: Number, required: true },
  time_limit: { type: Number, required: true }
}, {
  timestamps: true,
  versionKey: false
});

export interface IExamResult extends Document {
  user: number;
  code: string;
  right_answers: number;
  wrong_answers: number;
  unanswers: number;
  total_questions: number;
  timetsamp_started: Date;
  timetsamp_ended: Date;
  score: string;
  passing_score: number;
}

export const ExamResultSchema = new Schema<IExamResult>({
  user: { type: Number, required: true },
  code: { type: String, required: true },
  right_answers: { type: Number, required: true },
  wrong_answers: { type: Number, required: true },
  unanswers: { type: Number, required: true },
  total_questions: { type: Number, required: true },
  timetsamp_started: { type: Date, required: true },
  timetsamp_ended: { type: Date, required: true },
  score: { type: String, required: true },
  passing_score: { type: Number, required: true }
}, {
  timestamps: true,
  versionKey: false
});
