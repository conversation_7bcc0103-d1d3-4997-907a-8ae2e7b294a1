import axios from 'axios';
import * as dotenvx from '@dotenvx/dotenvx';
import { cache } from '../utils/cacheable';
import { logger } from '../utils/logger';

export interface GoogleTokenInfo {
  aud: string;
  azp: string;
  email: string;
  email_verified: string;
  exp: string;
  family_name?: string;
  given_name?: string;
  hd?: string;
  iat: string;
  iss: string;
  jti?: string;
  name?: string;
  picture?: string;
  sub: string; // Google user ID
}

export interface TokenValidationResult {
  isValid: boolean;
  userId?: string;
  googleId?: string;
  email?: string;
  error?: string;
  message?: string;
  code?: string;
}

export class GoogleTokenService {
  private readonly tokenInfoUrl: string;
  private readonly cacheTtl: number;
  private readonly testToken: string;

  constructor() {
    this.tokenInfoUrl = dotenvx.get('GOOGLE_TOKEN_INFO_URL') || 'https://oauth2.googleapis.com/tokeninfo';
    this.cacheTtl = parseInt(dotenvx.get('GOOGLE_TOKEN_CACHE_TTL') || '3600') * 1000; // Convert to milliseconds
    this.testToken = dotenvx.get('TEST_TOKEN') || 'test-token';
  }

  /**
   * Validate Google ID token and return user information
   */
  async validateToken(token: string): Promise<TokenValidationResult> {
    const requestId = `token-${Date.now()}`;
    logger.debug(`[${requestId}] Validating Google token`, {
      tokenLength: token ? token.length : 0,
      tokenPreview: token ? `${token.substring(0, 10)}...` : 'none'
    });

    try {
      // Handle test token for development
      if (dotenvx.get('MODE') !== 'production' && token === this.testToken) {
        logger.debug(`[${requestId}] Using test token for development`);
        return {
          isValid: true,
          userId: 'test-user-id',
          googleId: 'test-google-id',
          email: '<EMAIL>'
        };
      }

      // Check cache first
      const cacheKey = `google-token:${token}`;
      const cachedResult = await cache.get<TokenValidationResult>(cacheKey);

      if (cachedResult) {
        logger.debug(`[${requestId}] Token validation result found in cache`);
        return cachedResult;
      }

      // Validate token with Google
      logger.debug(`[${requestId}] Calling Google token validation API`);
      logger.debug(`[${requestId}] token:` + token);
      const response = await axios.get(`${this.tokenInfoUrl}?id_token=${token}`, {
        timeout: 10000, // 10 second timeout
        headers: {
          'User-Agent': 'NodeJS-Server/1.0'
        }
      });

      const tokenInfo: GoogleTokenInfo = response.data;
      logger.debug(`[${requestId}] Google token validation successful`, {
        sub: tokenInfo.sub,
        email: tokenInfo.email,
        exp: tokenInfo.exp
      });

      // Check if token is expired
      const expirationTime = parseInt(tokenInfo.exp) * 1000; // Convert to milliseconds
      const currentTime = Date.now();

      if (currentTime >= expirationTime) {
        logger.warn(`[${requestId}] Token is expired`, {
          exp: expirationTime,
          current: currentTime
        });
        return {
          isValid: false,
          error: 'Token expired',
          message: 'Your session has expired. Please log in again.',
          code: 'TOKEN_EXPIRED'
        };
      }

      // Create validation result
      const result: TokenValidationResult = {
        isValid: true,
        googleId: tokenInfo.sub,
        email: tokenInfo.email
      };

      // Cache the result (use shorter TTL if token expires soon)
      const timeUntilExpiry = expirationTime - currentTime;
      const cacheTtl = Math.min(this.cacheTtl, timeUntilExpiry - 60000); // Cache for 1 minute less than expiry

      if (cacheTtl > 0) {
        await cache.set(cacheKey, result, cacheTtl);
        logger.debug(`[${requestId}] Token validation result cached`, {
          cacheTtl: cacheTtl / 1000
        });
      }

      return result;

    } catch (error) {
      const axiosError = error as any; // Type assertion for axios error

      // Enhanced error logging with more context
      const errorDetails = {
        requestId,
        tokenLength: token ? token.length : 0,
        tokenPreview: token ? `${token.substring(0, 20)}...${token.substring(token.length - 10)}` : 'none',
        errorMessage: axiosError.message || String(error),
        httpStatus: axiosError.response?.status,
        httpStatusText: axiosError.response?.statusText,
        responseData: axiosError.response?.data,
        responseHeaders: axiosError.response?.headers,
        requestUrl: `${this.tokenInfoUrl}?id_token=${token ? '[REDACTED]' : 'none'}`,
        axiosCode: axiosError.code,
        isNetworkError: axiosError.code === 'ENOTFOUND' || axiosError.code === 'ECONNREFUSED',
        isTimeout: axiosError.code === 'ECONNABORTED' || axiosError.message?.includes('timeout'),
        timestamp: new Date().toISOString()
      };

      logger.error(`[${requestId}] 🔐 Google token validation failed - Detailed Analysis`+ JSON.stringify(errorDetails));

      // Log specific error scenarios for easier debugging
      if (axiosError.response?.status === 400) {
        logger.error(`[${requestId}] 🚫 Google API returned 400 - Invalid token format or malformed request`, {
          possibleCauses: [
            'Token is not a valid Google ID token',
            'Token is corrupted or truncated',
            'Token contains invalid characters',
            'Request format is incorrect'
          ],
          tokenAnalysis: {
            length: token?.length,
            startsWithEyJ: token?.startsWith('eyJ'),
            containsDots: (token?.match(/\./g) || []).length,
            isJWTFormat: token?.split('.').length === 3
          }
        });
      } else if (axiosError.response?.status === 401) {
        logger.error(`[${requestId}] 🔒 Google API returned 401 - Token verification failed`, {
          possibleCauses: [
            'Token has expired',
            'Token was issued for different client ID',
            'Token signature is invalid',
            'Token has been revoked'
          ]
        });
      } else if (axiosError.code === 'ENOTFOUND' || axiosError.code === 'ECONNREFUSED') {
        logger.error(`[${requestId}] 🌐 Network error - Cannot reach Google token validation service`, {
          possibleCauses: [
            'No internet connection',
            'DNS resolution failed',
            'Google services are down',
            'Firewall blocking requests'
          ],
          googleTokenUrl: this.tokenInfoUrl
        });
      } else if (axiosError.code === 'ECONNABORTED' || axiosError.message?.includes('timeout')) {
        logger.error(`[${requestId}] ⏰ Request timeout - Google token validation took too long`, {
          timeoutMs: 10000,
          possibleCauses: [
            'Slow internet connection',
            'Google services experiencing high load',
            'Network congestion'
          ]
        });
      } else {
        logger.error(`[${requestId}] ❓ Unexpected error during Google token validation`, {
          errorType: typeof error,
          errorConstructor: error?.constructor?.name,
          hasResponse: !!axiosError.response,
          hasRequest: !!axiosError.request
        });
      }

      // Handle specific Google API errors
      if (axiosError.response?.status === 400) {
        return {
          isValid: false,
          error: 'Invalid token format',
          message: 'The provided token is not in a valid format. Please log in again.',
          code: 'INVALID_TOKEN_FORMAT'
        };
      }

      if (axiosError.response?.status === 401) {
        return {
          isValid: false,
          error: 'Token verification failed',
          message: 'Token verification failed. Please log in again.',
          code: 'TOKEN_VERIFICATION_FAILED'
        };
      }

      return {
        isValid: false,
        error: 'Token validation service unavailable',
        message: 'Unable to validate token at this time. Please try again later.',
        code: 'SERVICE_UNAVAILABLE'
      };
    }
  }

  /**
   * Store token-to-userId mapping for authenticated sessions
   */
  async storeTokenUserMapping(token: string, userId: string, googleId: string): Promise<void> {
    try {
      const mappingKey = `token-user:${token}`;
      const mapping = {
        userId,
        googleId,
        timestamp: Date.now()
      };

      await cache.set(mappingKey, mapping, this.cacheTtl);
      logger.debug('Token-user mapping stored', {
        userId,
        googleId,
        tokenLength: token.length
      });
    } catch (error) {
      logger.error('Failed to store token-user mapping', { error, userId });
      throw error;
    }
  }

  /**
   * Get user ID from token mapping
   */
  async getUserFromToken(token: string): Promise<{ userId: string; googleId: string } | null> {
    try {
      const mappingKey = `token-user:${token}`;
      const mapping = await cache.get<{ userId: string; googleId: string; timestamp: number }>(mappingKey);

      if (mapping) {
        logger.debug('Token-user mapping found', {
          userId: mapping.userId,
          googleId: mapping.googleId
        });
        return {
          userId: mapping.userId,
          googleId: mapping.googleId
        };
      }

      return null;
    } catch (error) {
      logger.error('Failed to get user from token mapping', { error });
      return null;
    }
  }

  /**
   * Invalidate token from cache
   */
  async invalidateToken(token: string): Promise<void> {
    try {
      const cacheKey = `google-token:${token}`;
      const mappingKey = `token-user:${token}`;

      await Promise.all([
        cache.delete(cacheKey),
        cache.delete(mappingKey)
      ]);

      logger.debug('Token invalidated from cache', {
        tokenLength: token.length
      });
    } catch (error) {
      logger.error('Failed to invalidate token', { error });
    }
  }

  /**
   * Get token expiration time from Google token info
   */
  async getTokenExpiration(token: string): Promise<number | null> {
    try {
      const validation = await this.validateToken(token);
      if (!validation.isValid) {
        return null;
      }

      // If it's a test token, return a future expiration
      if (token === this.testToken) {
        return Date.now() + (24 * 60 * 60 * 1000); // 24 hours from now
      }

      // For real tokens, we need to call Google API to get expiration
      const response = await axios.get(`${this.tokenInfoUrl}?id_token=${token}`);
      const tokenInfo: GoogleTokenInfo = response.data;

      return parseInt(tokenInfo.exp) * 1000; // Convert to milliseconds
    } catch (error) {
      logger.error('Failed to get token expiration', { error });
      return null;
    }
  }
}

// Export singleton instance
export const googleTokenService = new GoogleTokenService();