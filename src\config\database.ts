import mongoose, { Model, Schema } from 'mongoose';
import * as dotenvx from '@dotenvx/dotenvx';

export const connectDB = async (
  maxRetries: number = 5,
  retryDelay: number = 1000
): Promise<void> => {
  let retries = 0;
  const connectionLink = dotenvx.get('MONGODB_URI');

  if (!connectionLink) {
    throw new Error("MONGODB_URI environment variable is not defined");
  }

  const maskCredentials = (str: string): string => {
    if (str.length <= 2) return str;
    return `${str[0]}${'*'.repeat(str.length - 2)}${str.slice(-1)}`;
  };

  const maskedURI = connectionLink.replace(
    /(:\/\/)([^:]+):([^@]+)@/,
    (_, protocol, user, pass) =>
      `${protocol}${maskCredentials(user)}:${maskCredentials(pass)}@`
  );

  while (retries <= maxRetries) { // Changed to <= for correct retry count
    console.log(`Connecting to MongoDB... (Attempt ${retries + 1}/${maxRetries + 1})`);
    console.log(`Using URI: ${maskedURI}`);

    try {
      await mongoose.connect(connectionLink, {
        serverSelectionTimeoutMS: 10000
      });
      console.log('✅ MongoDB connected');

      mongoose.set('debug', (collectionName, method, query, doc) => {
        console.log(`🐢 Mongoose: ${collectionName}.${method}`, {
          query: JSON.stringify(query),
          doc: doc && JSON.stringify(doc)
        });
      });

      return;
    } catch (err) {
      const error = err as Error;
      console.error(`❌ Attempt ${retries + 1} failed: ${error.message}`);

      if (retries >= maxRetries) {
        console.error(`💥 All ${maxRetries + 1} attempts exhausted`);
        throw new Error(`Connection failed after ${maxRetries + 1} attempts`);
      }

      retries++;
      console.log(`⌛ Retrying in ${retryDelay / 1000} sec...`);
      await new Promise(resolve => setTimeout(resolve, retryDelay));
    }
  }
};

export const connectDB2 = async (): Promise<void> => {
  const { MongoClient } = require("mongodb");
  // Replace the uri string with your connection string.
  let uri = dotenvx.get('MONGODB_URI') || ''
  const client = new MongoClient(uri);
  console.log('🔌 Attempting to connect to MongoDB via mongodb lib...');
  try {
    // Connect to MongoDB
    await client.connect();
    console.log('✅ Successfully connected to MongoDB');

    // Access the database and collection
    const database = client.db('exam');
    console.log('📂 Accessed database:', database.databaseName);

    const movies = database.collection('detail');
    console.log('📄 Accessed collection:', movies.collectionName);

    // Query for a document with the specified code
    const query = { code: 'abc' };
    console.log('🔍 Executing query:', JSON.stringify(query));

    const movie = await movies.findOne(query);
    if (movie) {
      console.log('🎬 Found document:', JSON.stringify(movie, null, 2));
    } else {
      console.log('❌ No document found matching the query');
    }
  } catch (err) {
    const errorMessage = (err as Error).message || 'Unknown error occurred';
    console.error(`❌ Connection attempt failed:`, errorMessage);
    throw err; // Re-throw the error to propagate it
  } finally {
    // Ensure the client is closed, even if an error occurs
    console.log('🔌 Closing MongoDB connection...');
    await client.close();
    console.log('✅ MongoDB connection closed');
  }
};
export const getCollections = async (dbName: string): Promise<string[]> => {
  try {
    const db = mongoose.connection.useDb(dbName);
    const collections = await db.listCollections();
    const collectionNames = collections.map((col: any) => col.name);
    return collectionNames;
  } catch (error) {
    console.error('Error getting collections:', error);
    throw error;
  }
};

export const getCollection = async (dbName: string, collectionName: string): Promise<any> => {
  const db = mongoose.connection.useDb(dbName);
  return db.collection(collectionName);
};

// Define a generic schema (adjust as needed)
const genericSchema = new Schema({}, { strict: false }); // Allows flexible documents

// Cache models to avoid OverwriteModelError
const modelCache = new Map<string, Model<any>>();

export const getModel = async (
  dbName: string,
  collectionName: string
): Promise<Model<any>> => {
  const connectionLink = dotenvx.get('MONGODB_URI');

  if (!connectionLink) {
    throw new Error("MONGODB_URI environment variable is not defined");
  }

  // Masking function remains the same
  const maskCredentials = (str: string): string => {
    if (str.length <= 2) return str;
    return `${str[0]}${'*'.repeat(str.length - 2)}${str.slice(-1)}`;
  };

  // Connection handling remains the same
  if (mongoose.connection.readyState !== 1) {
    const maskedURI = connectionLink.replace(
      /(:\/\/)([^:]+):([^@]+)@/,
      (_, protocol, user, pass) =>
        `${protocol}${maskCredentials(user)}:${maskCredentials(pass)}@`
    );
    console.log(`getModel connecting to database: ${maskedURI}`);
    await mongoose.connect(connectionLink);
  }

  const db = mongoose.connection.useDb(dbName);
  const cacheKey = `${dbName}.${collectionName}`;

  // Return cached model if available
  if (modelCache.has(cacheKey)) {
    console.log(`♻️ Using cached model for ${cacheKey}`);
    return modelCache.get(cacheKey)!;
  }

  // Create schema with versioning disabled and strict mode off
  const dynamicSchema = new mongoose.Schema({}, {
    // Disable version key
    versionKey: false,
    // Prevent "strict mode" errors for unknown fields
    strict: false,
    // Add timestamps automatically (optional)
    timestamps: false,
    // Customize toJSON transformation
    toJSON: {
      transform: (doc, ret) => {
        // Remove Mongoose internal properties
        delete ret._id;
        delete ret.__v;
        return ret;
      }
    }
  });

  // Create and cache model
  console.log(`🆕 Creating new model for ${cacheKey}`);
  const model = db.model(collectionName, dynamicSchema, collectionName);
  modelCache.set(cacheKey, model);

  return model;
};