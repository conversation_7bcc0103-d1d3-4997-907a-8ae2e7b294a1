import moment from 'moment';
import { apiConfig } from '../../config/apiConfig';
import { getCollection, getCollections, getModel } from '../../config/database';
import { DbOperations } from '../../utils/dbLogger';
import { isCorrectAnswer } from '../../utils/appHelper';
import { IExam } from './exam.model';
import { Types } from 'mongoose';
import { ObjectId } from 'mongodb';
import { cacheable, cacheInvalidator } from '../../utils/cacheable';
import { logger } from '../../utils/logger'
import * as dotenvx from '@dotenvx/dotenvx';
export class ExamService {

    @cacheable()
    static async getVendorList(searchKeyword: string): Promise<any> {
        logger.debug("searchKeyword: ", searchKeyword);
        const collectionVendor = await getModel(apiConfig.databaseName.exam, "vendor");
        const detailsCollection = await getModel(apiConfig.databaseName.exam, "detail");
        const vendors = await DbOperations.find(collectionVendor);
        const examDetails = await DbOperations.find(detailsCollection, {
            vendor: { $in: vendors.map((vendor: any) => vendor._id.toString()) }
        });
        const vendorsResult: any[] = [];

        vendors.map((vendor: any, index: number) => {
            let canAdd = false
            const cVendorId = vendor._id.toString();
            const vendorItem = JSON.parse(JSON.stringify(vendor));
            vendorItem.exams = [];
            if (vendorItem.name.toLowerCase().includes(searchKeyword.toLowerCase()) ||
                vendorItem.code.toLowerCase().includes(searchKeyword.toLowerCase())) {
                canAdd = true;
            }
            if (examDetails != undefined && examDetails.length > 0) {
                const foundPrevious = canAdd ? true : false;
                examDetails.map((examDetail: any) => {
                    if (examDetail.vendor === cVendorId) {
                        if (foundPrevious) {
                            vendorItem.exams.push(examDetail);
                        } else {
                            if (examDetail.name.toLowerCase().includes(searchKeyword.toLowerCase())) {
                                vendorItem.exams.push(examDetail);
                                canAdd = true;
                            }
                        }
                    }
                });
            }
            if (canAdd) {
                vendorsResult.push(vendorItem);
            }
        });
        return vendorsResult;
    }

    @cacheable()
    static async getExamList(vendor_code?: string): Promise<any[]> {
        try {
            // 获取考试模型
            const examModel = await getModel(apiConfig.databaseName.exam, "detail");

            let param: Record<string, any> = {
                 vendor_code: vendor_code || dotenvx.get('QNA_VENDOR'),
                 is_popular: true
             };

            // 使用 DbOperations 查询数据
            const exams = await DbOperations.find(
                examModel,
                param
            );

            // 获取供应商模型
            const vendorModel = await getModel(apiConfig.databaseName.exam, "vendor");

            // 使用 DbOperations 获取供应商数据
            const vendors = await DbOperations.find(vendorModel, {
                code: { $in: [...new Set(exams.map((e: any) => e.vendor_code))] }
            });

            // 创建供应商映射表
            const vendorMap = new Map(
                vendors.map((vendor: any) => [vendor.code, vendor.name])
            );

            // 转换 Mongoose 文档为普通对象
            return exams.map((exam: any) => ({
                //exam_id: exam._id.toString(),
                id: exam.code,
                exam_code: exam.exam_code,
                exam_name: exam.exam_code ? exam.name.replace(new RegExp(exam.exam_code.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'gi'), '').replace(/\s*[()]\s*/g, ' ').trim() : null,
                //total_questions: exam.total_questions,
                total_questions: exam.total_questions_mc_or_mc2,
                date_updated: exam.date_updated,
                /* timestamp_updated: exam.timestamp_updated,
                timestamp: exam.timestamp, */
                vendor_name: vendorMap.get(exam.vendor_code) || 'Unknown Vendor',
                subjects: exam.subjects || ["haha","hehe","haha2"]
                //vendor_code: exam.vendor_code,
                //is_popular: exam.is_popular
            }));

        } catch (error) {
            logger.error('ExamService Error:', error);
            throw error;
        }
    }

    @cacheable()
    static async getExamByCode(code: string): Promise<IExam | null> {
        const collection = await getModel(apiConfig.databaseName.exam, "detail");
        const vendorCollection = await getModel(apiConfig.databaseName.exam, "vendor");
        const examData = await DbOperations.findOne(collection, { code: code });
        const vendorData = await DbOperations.findOne(vendorCollection, { _id: new ObjectId(examData?.vendor) });
        examData.vendor = vendorData;
        return examData as IExam | null;
    }

    static async submitExam(
        code: string,
        user_answers: any[],
        start_time: string,
        end_time: string
    ): Promise<any> {
        const collectionQnA = await getCollection(apiConfig.databaseName.qna, code);
        const questionSourceList = await collectionQnA.find({}).toArray();
        let rightAnswers = 0;
        let wrongAnswers = 0;
        let unanswers = 0;
        let arrRsStatistic: any[] = []
        user_answers.map((answer, index) => {
            const questionSource = questionSourceList.find((question: any) => question._id.toString() === answer.question_id);
            let status = 0;
            if (questionSource) {
                if (answer.answer === "") {
                    status = 0;
                    unanswers++;
                } else {
                    if (isCorrectAnswer(answer.answer, questionSource.exam.answer)) {
                        rightAnswers++;
                        status = 1;
                    } else {
                        wrongAnswers++;
                        status = 2;
                    }
                }
            }
            arrRsStatistic.push({
                code: code,
                qna_id: answer.question_id,
                answer_taken: answer.answer,
                exam_result_id: "",
                timestamp_finished: end_time,
                is_correct: status === 1 ? true : false,
                is_unanswer: status === 0 ? true : false,
                user: 0
            });
        });
        const examInformation = await ExamService.getExamByCode(code);
        // build details result
        const detailsResult: any[] = [];
        questionSourceList.map((question: any, index: number) => {
            const userAnswer = user_answers.find((answer) => answer.question_id == question._id.toString());
            if (userAnswer) {
                const detailChoices: any[] = [];
                question.exam.choices.map((choice: any, index: number) => {
                    const code = Object.keys(choice)[0];
                    detailChoices.push({
                        code: code,
                        text: choice[code],
                        expected: question.exam.answer.includes(code),
                        user_answer: userAnswer.answer.includes(code)
                    });
                });
                logger.debug("userAnswer.is_flagged: ", userAnswer.question_id, userAnswer.is_flagged);
                const questionResultItem = {
                    qna_id: question._id.toString(),
                    question: question.exam.question,
                    choices: detailChoices,
                    answer: question.exam.answer,
                    user_answer: user_answers.find((answer) => answer.question_id === question._id.toString())?.answer || "",
                    is_flagged: userAnswer.is_flagged
                }
                detailsResult.push(questionResultItem);
            }
        });
        return {
            right_answers: rightAnswers,
            wrong_answers: wrongAnswers,
            unanswers: unanswers,
            code: code,
            total_questions: questionSourceList.length,
            timetsamp_started: start_time,
            timetsamp_ended: end_time,
            score: `${((rightAnswers / questionSourceList.length) * 100).toFixed(2)}%`,
            passing_score: examInformation ? examInformation.passing_score : 0,
            details_result: detailsResult,
            result_qna: arrRsStatistic
        };
    }

    static async updateAnswerStatistics(arrayData: any[]) {
        // 0 unanswer, 1 correct, 2 wrong
        // code: string, qna_id: string, answer_taken: string, exam_result_id: any, timestamp_finished: number, is_correct: boolean, is_unanswer: boolean, user: string
        const collection = await getCollection(apiConfig.databaseName.exam, "result_qna");
        await collection.insertMany(arrayData);
    }

    @cacheable()
    static async searchExam(keyword: string): Promise<any> {
        try {
            // Get details collection and search by name
            const detailsCollection = await getCollection(apiConfig.databaseName.exam, "detail");
            const vendorCollection = await getCollection(apiConfig.databaseName.exam, "vendor");

            const searchResults = await detailsCollection.find({
                name: { $regex: keyword, $options: 'i' }
            }).toArray();

            if (!searchResults || searchResults.length === 0) {
                return [];
            }

            // Get all unique vendor IDs from search results
            const vendorIds = [...new Set(searchResults.map((item: any) => item.vendor))];
            logger.debug("vendorIds: ", vendorIds);
            // Find all vendors in one query
            const vendors = await vendorCollection.find({
                _id: { $in: vendorIds.map((id: any) => new ObjectId(id)) }
            }).toArray();

            logger.debug("vendor: ", JSON.stringify(vendors))

            // Create vendor lookup map for faster access
            const vendorMap = vendors.reduce((acc: any, vendor: any) => {
                acc[vendor._id.toString()] = vendor;
                return acc;
            }, {});

            // Merge vendor info into search results
            const resultsWithVendor = searchResults.map((item: any) => {
                const vendorInfo = vendorMap[item.vendor.toString()];
                return {
                    ...item,
                    vendor_id: item.vendor,
                    vendor: vendorInfo
                };
            });

            return resultsWithVendor;
        } catch (error) {
            logger.error('Error searching exam:', error);
            return null;
        }
    }

    static async saveExamResult(data: any): Promise<any> {
        const collection = await getCollection(apiConfig.databaseName.exam, "result");
        const result = await collection.insertOne(data);
        return result;
    }

    @cacheable()
    static async getExamHistory(userId: string): Promise<any> {
        const collection = await getCollection(apiConfig.databaseName.exam, "result");
        const exams = await collection.find({ user: userId }).toArray();
        // Get exam details collection
        const detailsCollection = await getCollection(apiConfig.databaseName.exam, "detail");

        logger.debug("exams: ", exams);
        // Get exam names for each exam result
        const examDetails = await detailsCollection.find({
            code: { $in: exams.map((exam: any) => exam.code) }
        }).toArray();
        logger.debug("examDetails: ", examDetails);

        // Exam name, Score, Total Questions, Answers corrected, Started Time, Duration
        const combinedData: any[] = []
        exams.map((exam: any) => {
            const examDetail = examDetails.find((detail: any) => detail.code === exam.code);
            if (examDetail) {
                combinedData.push({
                    id: exam._id.toString(),
                    exam_name: examDetail.name,
                    score: exam.right_answers + " out of " + examDetail.total_questions,
                    total_questions: examDetail.total_questions,
                    answers_corrected: exam.right_answers,
                    started_time: moment(exam.timetsamp_started).format(apiConfig.formatDateTime.defaultDateTime),
                    duration: moment(exam.timetsamp_ended).diff(moment(exam.timetsamp_started), 'seconds')
                });
            }
        });
        return combinedData;
    }

    @cacheable()
    static async getExamResult(id: string): Promise<any> {
        const collection = await getCollection(apiConfig.databaseName.exam, "result");
        const resultId = new Types.ObjectId(id);
        const exam = await collection.findOne({ _id: resultId });
        logger.debug("exam: ", exam);
        return exam;
    }

    @cacheable()
    static async getExamResultStatisticQna(code: string, userId: number): Promise<any> {
        const collection = await getCollection(apiConfig.databaseName.exam, "result_qna");
        const result = await collection.find({ code: code, user: userId }).sort({ qna_id: 1 }).toArray();
        logger.debug("result: ", result);
        const resultQna: any[] = []
        result.map((item: any) => {
            let addedRsIndex = -1;
            resultQna.map((itemAdded: any, index: number) => {
                if (item.qna_id === itemAdded.qna_id) {
                    addedRsIndex = index;
                }
            });
            if (addedRsIndex === -1) {
                let addItem = {
                    qna_id: item.qna_id,
                    correct_times: item.is_correct ? 1 : 0,
                    wrong_times: item.is_correct ? 0 : 1,
                    unanswer_times: item.is_unanswer ? 1 : 0,
                    total_times: 1
                }
                resultQna.push(addItem);
            } else {
                resultQna[addedRsIndex].correct_times += item.is_correct ? 1 : 0;
                resultQna[addedRsIndex].wrong_times += item.is_correct ? 0 : 1;
                resultQna[addedRsIndex].unanswer_times += item.is_unanswer ? 1 : 0;
                resultQna[addedRsIndex].total_times += 1;
            }
        });
        return resultQna;
    }
}