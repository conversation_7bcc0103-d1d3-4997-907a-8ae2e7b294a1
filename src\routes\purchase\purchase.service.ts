import moment from "moment";
import { apiConfig } from "../../config/apiConfig";
import { getCollection } from "../../config/database";
import axios from 'axios';
import https from 'https';
import * as dotenvx from '@dotenvx/dotenvx';

const stripe = require("stripe")(dotenvx.get('STRIPE_SECRET_KEY'));


const axiosInstance = axios.create({
    httpsAgent: new https.Agent({
        rejectUnauthorized: false // Only use in development!
    })
});

export class PurchaseService {

    static async getStripeSessionDetails(sessionId: string): Promise<any> {
        try {
            const session = await stripe.checkout.sessions.retrieve(sessionId);
            console.log(JSON.stringify(session));
            return session;
          } catch (error) {
            console.error('Error retrieving session:', error);
            return null;
          }
    }

    static async checkWooOrderCreated(user_id: number, product_id: number, code: string): Promise<any> {
        try {
            const collection = await getCollection(apiConfig.databaseName.payment, "history");
            const payment = await collection.findOne({
                user: parseInt(user_id.toString()), 
                package_id: parseInt(product_id.toString()),
                code: code
            });
            console.log("payment: ", JSON.stringify(payment));
            if (payment != null && payment.order_id != null && payment.order_id != '') {
                const response = await axiosInstance.get(
                    `${apiConfig.wooEndpoint}/orders/${payment.order_id}`,
                    {
                        auth: {
                            username: dotenvx.get('WOO_CONSUMER_KEY') || '',
                            password: dotenvx.get('WOO_CONSUMER_SECRET') || '',
                        },
                    }
                );
                console.log("checkWooOrderCreated: ", JSON.stringify(response.data));
                return response.data;
            } else {
                return null;
            }
        } catch (error) {
            console.error('Error checking existing order:', error);
            return null;
        }
    }

    static async createWooOrder(sessionId: string, productId: string, code: string, user_id: number): Promise<any> {
        const session = await this.getStripeSessionDetails(sessionId);
        if (session != null && session.id != null && session.id != '') {
            // create woo order
            console.log("createWooOrder session: ", JSON.stringify(session));
            try {
                // check customer id exist
                const stripeCollection = await getCollection(apiConfig.databaseName.payment, "stripe");
                const stripeCustomer = await stripeCollection.findOne({stripe_user_email: session.customer_email});
                let customerId = null;
                let paymentMethodId = null;
                if (stripeCustomer == null || stripeCustomer.stripe_customer_id == null || stripeCustomer.stripe_customer_id == '') {
                    // create customer and payment method with stripe                    
                    customerId = session.customer;
                    console.log("customerId: ", customerId);
                    const paymentMethod = await stripe.paymentMethods.list({
                        customer: customerId,
                        type: 'card',
                    });
                    console.log("paymentMethod: ", JSON.stringify(paymentMethod));
                    if (paymentMethod.data.length > 0) {
                        paymentMethodId = paymentMethod.data[0].id;
                        console.log("paymentMethodId: ", paymentMethodId);
                        stripeCollection.insertOne({
                            user_id: parseInt(user_id.toString()),
                            stripe_customer_id: customerId,
                            stripe_payment_method_id: paymentMethodId,
                            stripe_user_email: session.customer_email,
                            exam_code: code,
                            payment_intent: session.payment_intent
                        });
                    }
                } else {
                    customerId = stripeCustomer.stripe_customer_id;
                    paymentMethodId = stripeCustomer.stripe_payment_method_id;
                }

                const createdOrder = await this.checkWooOrderCreated(user_id, parseInt(productId.toString()), code);
                if (createdOrder != null) {
                    return createdOrder;
                }
                // define order data
                const orderData = {
                  payment_method: 'stripe',
                  payment_method_title: 'Stripe',
                  set_paid: session.payment_status === 'paid', // payment status
                  billing: {
                    email: session.customer_email,
                    first_name: session.customer_details.name,
                  },
                  line_items: [
                    {
                      product_id: productId, // woo product id
                      quantity: 1, // amount total
                    },
                  ],
                  meta_data: [
                    {
                      key: 'stripe_session_id',
                      value: session.id, // save stripe session id
                    },
                    {
                      key: 'exam_code',
                      value: code, // save exam code
                    },
                    {
                      key: 'stripe_payment_intent',
                      value: session.payment_intent, // save payment intent
                    },
                  ],
                };
            
                // send order data to WooCommerce
                const response = await axiosInstance.post(
                  `${apiConfig.wooEndpoint}/orders`,
                  orderData,
                  {
                    auth: {
                        username: dotenvx.get('WOO_CONSUMER_KEY') || '',
                        password: dotenvx.get('WOO_CONSUMER_SECRET') || '',
                    },
                  },
                );                        
                if (response.data.id != null && response.data.id != '') {
                    const paymentId = await this.saveUserPayment(response.data.id, code, user_id, productId);
                    if (paymentId != null && paymentId != '') {
                        return response.data;
                    } else {
                        return null;
                    }
                } else {
                    return null;
                }
              } catch (error) {
                console.error('Error creating WooCommerce order:', error);
                return null;
              }
        } else {
            return null;
        }
    }

    static async saveUserPayment(orderId: string, code: string, user_id: number, product_id: string): Promise<any> {
        try {
            const collection = await getCollection(apiConfig.databaseName.payment, "history");
            const payment = await collection.insertOne({
                order_id: orderId,
                code: code,
                user: parseInt(user_id.toString()),
                package_id: parseInt(product_id.toString()),
                timetsamp_created: new Date(),
                timetsamp_expiry: new Date()
            });
            if (payment.insertedId != null && payment.insertedId != '') {
                return payment.insertedId;
            } else {
                return null;
            }
        } catch (error) {
            console.error('Error updating WooCommerce order:', error);
            return null;
        }
    }

    static async getPurchaseProductList(): Promise<any> {
        try {
            const wooEndpoint = apiConfig.wooEndpoint + '/products';
            const params = {
                consumer_key: dotenvx.get('WOO_CONSUMER_KEY'),
                consumer_secret: dotenvx.get('WOO_CONSUMER_SECRET'),
                per_page: 20  // Optional: limit results
            };
            const response = await axios.get(wooEndpoint, {
                params: params,  // Add as URL parameters instead of auth header
                httpsAgent: new (require('https').Agent)({  
                    rejectUnauthorized: false  // Disable SSL verification
                })
            });
            const products = [];
            for (const product of response.data) {
                let productItem = {
                    id: product.id,
                    name: product.name,
                    price: product.price,
                    regular_price: product.regular_price,
                    sale_price: product.sale_price,
                    short_description: product.short_description,
                    image: product.images.length > 0 ? product.images[0].src : null,
                    stock_status: product.stock_status
                }
                if (product.description != null && product.description != "") {
                    const pureDescription = product.description
                        .replace("<p>", "").replace("</p>", "")
                        .replace("<ul>", "").replace("</ul>", "")
                        .replace("<li>", "").replace("</li>", "")
                        .replace("<ol>", "").replace("</ol>", "")
                        .replace("<br>", "")
                        .replace("\n", "")
                    try {
                        const otherInfo = JSON.parse(pureDescription.toString());
                        productItem = {
                            ...productItem,
                            ...otherInfo
                        };
                    } catch (parseError) {
                        console.error('Error parsing product description:', parseError);
                        // Continue with original description if parsing fails
                    }
                }
                products.push(productItem);
            }
            return products; // product list
          } catch (error) {
            console.error('Error fetching products:', error);
            throw error;
          }
    }

    static async createCheckoutSession(
        price: number, 
        name: string, 
        image: string,
        exam_code: string, 
        email: string, 
        originUrl: string,
        wooProductId: string, 
        checkout_temp_id: string): Promise<any> {
        try {
            const mode = dotenvx.get('MODE') || 'development';
            console.log("mode: ", mode);
            const currentDate = new Date();
            const yearMonth = `${currentDate.getFullYear().toString().slice(-2)}${(currentDate.getMonth() + 1).toString().padStart(2, '0')}`;
            const productName = exam_code == "" ? name : `${exam_code}-${yearMonth}`;
            let imageUrl = image;
            if (mode == 'development') {
                if (name == 'Basics Plan') {
                    imageUrl = 'https://dummyimage.com/600x400/000/fff&text=Basic+plan';
                } else if (name == 'Main Plan') {
                    imageUrl = 'https://dummyimage.com/600x400/000/fff&text=Main+plan';
                } else {
                    imageUrl = 'https://dummyimage.com/600x400/000/fff&text=Dev+Env+Plan';
                }
            }
            if (imageUrl == '') {
                imageUrl = 'https://dummyimage.com/600x400/000/fff&text=No+image';
            }
            // load save payment method
            const stripeCollection = await getCollection(apiConfig.databaseName.payment, "stripe");
            const stripeCustomer = await stripeCollection.findOne({stripe_user_email: email});
            let paymentMethodId = null;
            let customerId = null;
            if (stripeCustomer != null && stripeCustomer.stripe_payment_method_id != null && stripeCustomer.stripe_payment_method_id != '') {
                paymentMethodId = stripeCustomer.stripe_payment_method_id;
                customerId = stripeCustomer.stripe_customer_id;
                const session = await stripe.checkout.sessions.create({
                    customer: customerId,
                    line_items: [{
                        price_data: {
                        currency: 'usd',
                        product_data: {
                            name: productName,
                            images: [imageUrl],
                            metadata: {
                                product_name: productName,
                                exam_code: exam_code
                            },
                        },
                        unit_amount: price * 100,
                        },
                        quantity: 1,
                    }],
                    payment_intent_data: {
                        description: productName + " - " + exam_code,
                        metadata: {
                            product_name: productName,
                            exam_code: exam_code
                        }
                    },
                    mode: 'payment',
                    ui_mode: 'embedded',
                    return_url: `${originUrl}/checkout?p_id=${wooProductId}&code=${exam_code}&m_ck_id=${checkout_temp_id}&str_id={CHECKOUT_SESSION_ID}`,
                    saved_payment_method_options: {
                        payment_method_save: 'enabled'
                    },
            });
            // return url form: http://localhost:3000/return?session_id=cs_test_a1gZusCepLLHAcd41S9KNTeL9XvxdqRBPJUtyV9glkqdKIchZE3jvqx2ja
            // console.log("session: ", session);
                return session.client_secret;
            } else {
                const session = await stripe.checkout.sessions.create({
                        customer_creation: 'always',
                        line_items: [{
                            price_data: {
                            currency: 'usd',
                            payment_intent_data: {
                                description: productName + " - " + exam_code,
                                metadata: {
                                    product_name: productName,
                                    exam_code: exam_code
                                }
                            },
                            product_data: {
                                name: productName,
                                images: [imageUrl],
                                metadata: {
                                    product_name: productName,
                                    exam_code: exam_code
                                },
                            },
                            unit_amount: price * 100,
                            },
                            quantity: 1,
                      }],
                      mode: 'payment',
                      customer_email: email,
                      ui_mode: 'embedded',
                      return_url: `${originUrl}/checkout?p_id=${wooProductId}&code=${exam_code}&m_ck_id=${checkout_temp_id}&str_id={CHECKOUT_SESSION_ID}`,
                      saved_payment_method_options: {
                        payment_method_save: 'enabled'
                      },
                });
                // return url form: http://localhost:3000/return?session_id=cs_test_a1gZusCepLLHAcd41S9KNTeL9XvxdqRBPJUtyV9glkqdKIchZE3jvqx2ja
                // console.log("session: ", session);
                return session.client_secret;
            }
          } catch (error) {
            console.error('Error creating checkout session:', error);
            return null;
          }
    }

    static async getMyPurchase(user_id: number): Promise<any> {
        const collection = await getCollection(apiConfig.databaseName.payment, "history");
        // const packageCollection = await getCollection(apiConfig.databaseName.payment, "package");
        const examCollection = await getCollection(apiConfig.databaseName.exam, "detail");
        const purchases = await collection.find({user: user_id}).toArray();
        const productList = await this.getPurchaseProductList();
        console.log("purchases: ", productList);
        const combinedPurchases = []
        for (const purchase of purchases) {
            let packageItem = null;
            for (const product of productList) {
                if (product.id == purchase.package_id) {
                    packageItem = product;
                    break;
                }
            }
            const examItem = await examCollection.findOne({code: purchase.code});
            combinedPurchases.push({
                purchase_id: purchase._id.toString(),
                package_name: packageItem?.name,
                package_description: packageItem?.short_description,
                package_id: packageItem?.id,
                exam_name: examItem?.name,
                exam_code: examItem?.code,
                purchase_date: moment(purchase.timetsamp_created).format(apiConfig.formatDateTime.defaultDate),
                expiry_date: moment(purchase.timetsamp_expiry).format(apiConfig.formatDateTime.defaultDate),
            });
        }
        return combinedPurchases;
    }
}