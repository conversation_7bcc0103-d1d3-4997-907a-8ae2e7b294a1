import * as zlib from 'zlib';
import { promisify } from 'util';
import { logger } from './logger';
import * as dotenvx from '@dotenvx/dotenvx';

// Promisify compression functions
const gzip = promisify(zlib.gzip);
const gunzip = promisify(zlib.gunzip);
const deflate = promisify(zlib.deflate);
const inflate = promisify(zlib.inflate);

export interface CompressionResult {
  compressed: string;
  originalSize: number;
  compressedSize: number;
  compressionRatio: number;
  algorithm: 'gzip' | 'deflate';
}

export interface DecompressionResult {
  decompressed: any;
  originalSize: number;
  decompressedSize: number;
}

/**
 * Compression utility for large payloads like AI chat history
 */
export class CompressionUtil {

  /**
   * Check if compression is enabled via environment variables
   */
  static isCompressionEnabled(): boolean {
    const compressionEnabled = dotenvx.get('ENABLE_COMPRESSION');

    // Default to true if not specified, false if explicitly disabled
    if (compressionEnabled === undefined || compressionEnabled === '') {
      return true; // Default enabled
    }

    return compressionEnabled.toLowerCase() === 'true';
  }

  /**
   * Check if request compression is enabled
   */
  static isRequestCompressionEnabled(): boolean {
    const requestCompressionEnabled = dotenvx.get('ENABLE_REQUEST_COMPRESSION');

    // Default to compression setting if not specified
    if (requestCompressionEnabled === undefined || requestCompressionEnabled === '') {
      return this.isCompressionEnabled();
    }

    return requestCompressionEnabled.toLowerCase() === 'true';
  }

  /**
   * Check if response compression is enabled
   */
  static isResponseCompressionEnabled(): boolean {
    const responseCompressionEnabled = dotenvx.get('ENABLE_RESPONSE_COMPRESSION');

    // Default to compression setting if not specified
    if (responseCompressionEnabled === undefined || responseCompressionEnabled === '') {
      return this.isCompressionEnabled();
    }

    return responseCompressionEnabled.toLowerCase() === 'true';
  }

  /**
   * Get compression threshold from environment
   */
  static getCompressionThreshold(): number {
    const threshold = dotenvx.get('COMPRESSION_THRESHOLD');

    if (threshold && !isNaN(parseInt(threshold))) {
      return parseInt(threshold);
    }

    return 1024; // Default 1KB threshold
  }

  /**
   * Log compression configuration
   */
  static logCompressionConfig(): void {
    logger.info('Compression Configuration', {
      compressionEnabled: this.isCompressionEnabled(),
      requestCompressionEnabled: this.isRequestCompressionEnabled(),
      responseCompressionEnabled: this.isResponseCompressionEnabled(),
      compressionThreshold: this.getCompressionThreshold(),
      compressionThresholdFormatted: this.formatBytes(this.getCompressionThreshold())
    });
  }

  /**
   * Compress data using gzip
   * @param data - Data to compress (object or string)
   * @param algorithm - Compression algorithm ('gzip' or 'deflate')
   * @returns Compressed data as base64 string with metadata
   */
  static async compressData(
    data: any,
    algorithm: 'gzip' | 'deflate' = 'gzip'
  ): Promise<CompressionResult> {
    try {
      // Check if compression is enabled
      if (!this.isCompressionEnabled()) {
        logger.debug('Compression is disabled via environment configuration');
        const jsonString = typeof data === 'string' ? data : JSON.stringify(data);
        const originalSize = Buffer.from(jsonString, 'utf8').length;

        return {
          compressed: jsonString,
          originalSize,
          compressedSize: originalSize,
          compressionRatio: 0,
          algorithm
        };
      }

      // Convert data to JSON string if it's an object
      const jsonString = typeof data === 'string' ? data : JSON.stringify(data);
      const originalBuffer = Buffer.from(jsonString, 'utf8');
      const originalSize = originalBuffer.length;

      // Choose compression algorithm
      const compressedBuffer = algorithm === 'gzip'
        ? await gzip(originalBuffer)
        : await deflate(originalBuffer);

      const compressedSize = compressedBuffer.length;
      const compressionRatio = ((originalSize - compressedSize) / originalSize) * 100;

      // Convert to base64 for transmission
      const compressed = compressedBuffer.toString('base64');

      logger.debug('Data compression completed', {
        algorithm,
        originalSize,
        compressedSize,
        compressionRatio: `${compressionRatio.toFixed(2)}%`
      });

      return {
        compressed,
        originalSize,
        compressedSize,
        compressionRatio,
        algorithm
      };
    } catch (error) {
      logger.error('Error compressing data', { error });
      throw new Error(`Compression failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Decompress data
   * @param compressedData - Base64 encoded compressed data
   * @param algorithm - Algorithm used for compression
   * @returns Decompressed data
   */
  static async decompressData(
    compressedData: string,
    algorithm: 'gzip' | 'deflate' = 'gzip'
  ): Promise<DecompressionResult> {
    try {
      // Convert from base64 to buffer
      const compressedBuffer = Buffer.from(compressedData, 'base64');
      const originalSize = compressedBuffer.length;

      // Decompress based on algorithm
      const decompressedBuffer = algorithm === 'gzip'
        ? await gunzip(compressedBuffer)
        : await inflate(compressedBuffer);

      const decompressedSize = decompressedBuffer.length;

      // Convert back to string and parse JSON
      const jsonString = decompressedBuffer.toString('utf8');
      let decompressed: any;

      try {
        decompressed = JSON.parse(jsonString);
      } catch {
        // If JSON parsing fails, return as string
        decompressed = jsonString;
      }

      logger.debug('Data decompression completed', {
        algorithm,
        originalSize,
        decompressedSize
      });

      return {
        decompressed,
        originalSize,
        decompressedSize
      };
    } catch (error) {
      logger.error('Error decompressing data', { error });
      throw new Error(`Decompression failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Check if data should be compressed based on size threshold
   * @param data - Data to check
   * @param threshold - Size threshold in bytes (uses environment setting if not provided)
   * @returns Whether data should be compressed
   */
  static shouldCompress(data: any, threshold?: number): boolean {
    // Check if compression is enabled first
    if (!this.isCompressionEnabled()) {
      return false;
    }

    // Use provided threshold or get from environment
    const actualThreshold = threshold !== undefined ? threshold : this.getCompressionThreshold();

    const jsonString = typeof data === 'string' ? data : JSON.stringify(data);
    const size = Buffer.from(jsonString, 'utf8').length;
    return size > actualThreshold;
  }

  /**
   * Get data size in bytes
   * @param data - Data to measure
   * @returns Size in bytes
   */
  static getDataSize(data: any): number {
    const jsonString = typeof data === 'string' ? data : JSON.stringify(data);
    return Buffer.from(jsonString, 'utf8').length;
  }

  /**
   * Format bytes to human readable string
   * @param bytes - Number of bytes
   * @returns Formatted string
   */
  static formatBytes(bytes: number): string {
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}

/**
 * Middleware for automatic compression/decompression
 */
export class CompressionMiddleware {

  /**
   * Express middleware to handle compressed requests
   */
  static handleCompressedRequest() {
    return async (req: any, res: any, next: any) => {
      try {
        // Check if request compression is enabled
        if (!CompressionUtil.isRequestCompressionEnabled()) {
          return next();
        }

        // Check if request contains compressed data
        const isCompressed = req.headers['content-encoding'] === 'gzip' ||
                           req.headers['x-compressed'] === 'true';

        if (isCompressed && req.body && req.body.compressed) {
          const algorithm = req.headers['x-compression-algorithm'] as 'gzip' | 'deflate' || 'gzip';

          logger.debug('Decompressing request data', {
            algorithm,
            compressedSize: req.body.compressed.length
          });

          const result = await CompressionUtil.decompressData(req.body.compressed, algorithm);
          req.body = result.decompressed;

          // Add decompression info to request
          req.decompressionInfo = {
            originalSize: result.originalSize,
            decompressedSize: result.decompressedSize,
            algorithm
          };
        }

        next();
      } catch (error) {
        logger.error('Error in compression middleware', { error });
        res.status(400).json({
          success: false,
          message: 'Failed to decompress request data',
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    };
  }

  /**
   * Express middleware to compress responses
   */
  static compressResponse(threshold?: number) {
    return async (req: any, res: any, next: any) => {
      const originalJson = res.json;

      res.json = async function(data: any) {
        try {
          // Check if response compression is enabled
          if (!CompressionUtil.isResponseCompressionEnabled()) {
            return originalJson.call(this, data);
          }

          // Check if client accepts compression
          const acceptsCompression = req.headers['accept-encoding']?.includes('gzip') ||
                                   req.headers['x-accept-compression'] === 'true';

          // Use provided threshold or get from environment
          const actualThreshold = threshold !== undefined ? threshold : CompressionUtil.getCompressionThreshold();

          if (acceptsCompression && CompressionUtil.shouldCompress(data, actualThreshold)) {
            const compressionResult = await CompressionUtil.compressData(data, 'gzip');

            // Set compression headers
            res.setHeader('Content-Encoding', 'gzip');
            res.setHeader('X-Compressed', 'true');
            res.setHeader('X-Compression-Algorithm', 'gzip');
            res.setHeader('X-Original-Size', compressionResult.originalSize.toString());
            res.setHeader('X-Compressed-Size', compressionResult.compressedSize.toString());
            res.setHeader('X-Compression-Ratio', compressionResult.compressionRatio.toFixed(2));

            logger.debug('Compressing response data', JSON.stringify({
              originalSize: CompressionUtil.formatBytes(compressionResult.originalSize),
              compressedSize: CompressionUtil.formatBytes(compressionResult.compressedSize),
              compressionRatio: `${compressionResult.compressionRatio.toFixed(2)}%`
            }));

            return originalJson.call(this, {
              compressed: compressionResult.compressed,
              metadata: {
                originalSize: compressionResult.originalSize,
                compressedSize: compressionResult.compressedSize,
                compressionRatio: compressionResult.compressionRatio,
                algorithm: compressionResult.algorithm
              }
            });
          }

          // No compression needed or not supported
          return originalJson.call(this, data);
        } catch (error) {
          logger.error('Error compressing response', { error });
          // Fall back to uncompressed response
          return originalJson.call(this, data);
        }
      };

      next();
    };
  }
}
