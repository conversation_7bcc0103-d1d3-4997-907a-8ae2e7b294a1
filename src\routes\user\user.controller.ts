import { Request, Response } from 'express';
import { UserService } from './user.service';
import { logger } from '../../utils/logger';

interface AuthenticatedRequest extends Request {
  user_id?: string;
  google_id?: string;
}

export class UserController {
  private userService: UserService;

  constructor() {
    this.userService = new UserService();
  }

  /**
   * Get user profile
   */
  async getUserProfile(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const userId = req.user_id;
      if (!userId) {
        res.status(401).json({ success: false, message: 'Unauthorized' });
        return;
      }

      const user = await this.userService.findUserById(userId);
      if (!user) {
        res.status(404).json({ success: false, message: 'User not found' });
        return;
      }

      // Return only profile data, not the entire user object
      res.json({
        success: true,
        profile: {
          id: user._id,
          email: user.email,
          displayName: user.displayName,
          profilePicture: user.profilePicture,
          createdAt: user.createdAt
        }
      });
    } catch (error) {
      logger.error({ error }, 'Error in getUserProfile controller');
      res.status(500).json({ success: false, message: 'Internal server error' });
    }
  }

  /**
   * Update user profile
   */
  async updateUserProfile(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const userId = req.user_id;
      if (!userId) {
        res.status(401).json({ success: false, message: 'Unauthorized' });
        return;
      }

      const profileData = req.body;
      const updatedUser = await this.userService.updateUserProfile(userId, profileData);

      if (!updatedUser) {
        res.status(404).json({ success: false, message: 'User not found' });
        return;
      }

      res.json({
        success: true,
        profile: {
          id: updatedUser._id,
          email: updatedUser.email,
          displayName: updatedUser.displayName,
          profilePicture: updatedUser.profilePicture,
          updatedAt: updatedUser.updatedAt
        }
      });
    } catch (error) {
      logger.error({ error }, 'Error in updateUserProfile controller');
      res.status(500).json({ success: false, message: 'Internal server error' });
    }
  }

  /**
   * Get user by Google ID (requires Google token authentication)
   */
  async getUserByGoogleId(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const userId = req.user_id;
      const googleId = req.google_id;

      if (!userId || !googleId) {
        res.status(401).json({ success: false, message: 'Unauthorized - valid Google token required' });
        return;
      }

      const { googleId: requestedGoogleId } = req.params;
      if (!requestedGoogleId) {
        res.status(400).json({ success: false, message: 'Google ID is required' });
        return;
      }

      // Verify that the authenticated user is requesting their own data or has permission
      // For now, we'll allow any authenticated user to request any user data
      // In production, you might want to add additional authorization checks

      const user = await this.userService.getUserByGoogleId(requestedGoogleId);

      if (!user) {
        res.status(404).json({ success: false, message: 'User not found' });
        return;
      }

      // Return the complete user object (excluding sensitive fields if needed)
      res.json({
        success: true,
        user: {
          id: user._id,
          googleId: user.googleId,
          email: user.email,
          displayName: user.displayName,
          profilePicture: user.profilePicture,
          createdAt: user.createdAt,
          updatedAt: user.updatedAt,
          progress: user.progress,
          quizResults: user.quizResults,
          purchases: user.purchases,
          aiCredits: user.aiCredits,
          aiChats: user.aiChats,
          loginHistory: user.loginHistory
        }
      });
    } catch (error) {
      logger.error({ error }, 'Error in getUserByGoogleId controller');
      res.status(500).json({ success: false, message: 'Internal server error' });
    }
  }

  /**
   * Get user progress
   */
  async getUserProgress(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const userId = req.user_id;
      if (!userId) {
        res.status(401).json({ success: false, message: 'Unauthorized' });
        return;
      }

      const courseId = req.query.courseId as string | undefined;
      const progress = await this.userService.getUserProgress(userId, courseId);

      if (progress === null) {
        res.status(404).json({ success: false, message: 'User or course not found' });
        return;
      }

      res.json({ success: true, progress });
    } catch (error) {
      logger.error({ error }, 'Error in getUserProgress controller');
      res.status(500).json({ success: false, message: 'Internal server error' });
    }
  }

  /**
   * Bulk update user progress for an entire exam
   */
  async updateUserProgressBulk(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const userId = req.user_id;
      if (!userId) {
        res.status(401).json({ success: false, message: 'Unauthorized' });
        return;
      }

      const { examId } = req.params;
      const progressData = req.body;

      logger.debug('Controller: Bulk updating user progress', { userId, examId, progressData });

      const updatedProgress = await this.userService.updateUserProgressBulk(userId, examId, progressData);

      if (!updatedProgress) {
        res.status(404).json({ success: false, message: 'User not found' });
        return;
      }

      logger.debug('Controller: Bulk progress updated successfully', { userId, examId });
      res.json({ success: true, progress: updatedProgress });
    } catch (error) {
      logger.error({ error }, 'Error in updateUserProgressBulk controller');
      res.status(500).json({ success: false, message: 'Internal server error' });
    }
  }

  /**
   * Update user progress for exam questions
   */
  async updateUserProgress(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const userId = req.user_id;
      if (!userId) {
        res.status(401).json({ success: false, message: 'Unauthorized' });
        return;
      }

      const { examId, subject } = req.params;
      const progressData = req.body;

      logger.debug('Controller: Updating user progress', { userId, examId, subject, progressData });

      const updatedProgress = await this.userService.updateUserProgress(userId, examId, subject, progressData);

      if (!updatedProgress) {
        res.status(404).json({ success: false, message: 'User not found' });
        return;
      }

      logger.debug('Controller: Progress updated successfully', { userId, examId, subject });
      res.json({ success: true, progress: updatedProgress });
    } catch (error) {
      logger.error({ error }, 'Error in updateUserProgress controller');
      res.status(500).json({ success: false, message: 'Internal server error' });
    }
  }

  /**
   * Get quiz results
   */
  async getQuizResults(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const userId = req.user_id;
      if (!userId) {
        res.status(401).json({ success: false, message: 'Unauthorized' });
        return;
      }

      const quizId = req.query.quizId as string | undefined;
      const results = await this.userService.getQuizResults(userId, quizId);

      if (results === null) {
        res.status(404).json({ success: false, message: 'User or quiz not found' });
        return;
      }

      res.json({ success: true, results });
    } catch (error) {
      logger.error({ error }, 'Error in getQuizResults controller');
      res.status(500).json({ success: false, message: 'Internal server error' });
    }
  }

  /**
   * Submit quiz result
   */
  async submitQuizResult(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const userId = req.user_id;
      if (!userId) {
        res.status(401).json({ success: false, message: 'Unauthorized' });
        return;
      }

      const quizResult = req.body;

      if (!quizResult.quizId) {
        res.status(400).json({ success: false, message: 'Quiz ID is required' });
        return;
      }

      const savedResult = await this.userService.submitQuizResult(userId, quizResult);

      if (!savedResult) {
        res.status(404).json({ success: false, message: 'User not found' });
        return;
      }

      res.json({ success: true, result: savedResult });
    } catch (error) {
      logger.error({ error }, 'Error in submitQuizResult controller');
      res.status(500).json({ success: false, message: 'Internal server error' });
    }
  }

  /**
   * Get user purchases
   */
  async getUserPurchases(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const userId = req.user_id;
      if (!userId) {
        res.status(401).json({ success: false, message: 'Unauthorized' });
        return;
      }

      const status = req.query.status as string | undefined;
      const purchases = await this.userService.getUserPurchases(userId, status);

      if (purchases === null) {
        res.status(404).json({ success: false, message: 'User not found' });
        return;
      }

      res.json({ success: true, purchases });
    } catch (error) {
      logger.error({ error }, 'Error in getUserPurchases controller');
      res.status(500).json({ success: false, message: 'Internal server error' });
    }
  }

  /**
   * Verify purchase
   */
  async verifyPurchase(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const userId = req.user_id;
      if (!userId) {
        res.status(401).json({ success: false, message: 'Unauthorized' });
        return;
      }

      const purchaseData = req.body;

      if (!purchaseData.productId || !purchaseData.transactionId || !purchaseData.platform || !purchaseData.receipt) {
        res.status(400).json({ success: false, message: 'Missing required purchase data' });
        return;
      }

      const result = await this.userService.verifyPurchase(userId, purchaseData);
      res.json(result);
    } catch (error) {
      logger.error({ error }, 'Error in verifyPurchase controller');
      res.status(500).json({ success: false, message: 'Internal server error' });
    }
  }

  /**
   * Get AI credits
   */
  async getAICredits(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const userId = req.user_id;
      if (!userId) {
        res.status(401).json({ success: false, message: 'Unauthorized' });
        return;
      }

      const credits = await this.userService.getAICredits(userId);

      if (credits === null) {
        res.status(404).json({ success: false, message: 'User not found' });
        return;
      }

      res.json({ success: true, credits });
    } catch (error) {
      logger.error({ error }, 'Error in getAICredits controller');
      res.status(500).json({ success: false, message: 'Internal server error' });
    }
  }

  /**
   * Get AI credit transactions
   */
  async getAICreditTransactions(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const userId = req.user_id;
      if (!userId) {
        res.status(401).json({ success: false, message: 'Unauthorized' });
        return;
      }

      const limit = parseInt(req.query.limit as string || '20', 10);
      const offset = parseInt(req.query.offset as string || '0', 10);

      const transactions = await this.userService.getAICreditTransactions(userId, limit, offset);

      if (transactions === null) {
        res.status(404).json({ success: false, message: 'User not found' });
        return;
      }

      res.json({ success: true, ...transactions });
    } catch (error) {
      logger.error({ error }, 'Error in getAICreditTransactions controller');
      res.status(500).json({ success: false, message: 'Internal server error' });
    }
  }

  /**
   * Purchase AI credits
   */
  async purchaseAICredits(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const userId = req.user_id;
      if (!userId) {
        res.status(401).json({ success: false, message: 'Unauthorized' });
        return;
      }

      const purchaseData = req.body;

      if (!purchaseData.amount || !purchaseData.paymentMethod) {
        res.status(400).json({ success: false, message: 'Missing required purchase data' });
        return;
      }

      const result = await this.userService.purchaseAICredits(userId, purchaseData);

      if (!result) {
        res.status(404).json({ success: false, message: 'User not found' });
        return;
      }

      res.json({ success: true, credits: result });
    } catch (error) {
      logger.error({ error }, 'Error in purchaseAICredits controller');
      res.status(500).json({ success: false, message: 'Internal server error' });
    }
  }

  /**
   * Get AI chat history
   */
  async getAIChatHistory(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const userId = req.user_id;
      if (!userId) {
        res.status(401).json({ success: false, message: 'Unauthorized' });
        return;
      }

      const chatHistory = await this.userService.getAIChatHistory(userId);

      if (chatHistory === null) {
        res.status(404).json({ success: false, message: 'User not found' });
        return;
      }

      res.json({ success: true, chatHistory });
    } catch (error) {
      logger.error({ error }, 'Error in getAIChatHistory controller');
      res.status(500).json({ success: false, message: 'Internal server error' });
    }
  }

  /**
   * Update AI chat history
   */
  async updateAIChatHistory(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const userId = req.user_id;
      if (!userId) {
        res.status(401).json({ success: false, message: 'Unauthorized' });
        return;
      }

      const chatHistoryData = req.body;
      const updatedChatHistory = await this.userService.updateAIChatHistory(userId, chatHistoryData);

      if (updatedChatHistory === null) {
        res.status(404).json({ success: false, message: 'User not found' });
        return;
      }

      res.json({ success: true, chatHistory: updatedChatHistory });
    } catch (error) {
      logger.error({ error }, 'Error in updateAIChatHistory controller');
      res.status(500).json({ success: false, message: 'Internal server error' });
    }
  }

  /**
   * Get a specific AI chat
   */
  async getAIChat(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const userId = req.user_id;
      if (!userId) {
        res.status(401).json({ success: false, message: 'Unauthorized' });
        return;
      }

      const chatId = req.params.chatId;
      const chat = await this.userService.getAIChat(userId, chatId);

      if (!chat) {
        res.status(404).json({ success: false, message: 'Chat not found' });
        return;
      }

      res.json({ success: true, chat });
    } catch (error) {
      logger.error({ error }, 'Error in getAIChat controller');
      res.status(500).json({ success: false, message: 'Internal server error' });
    }
  }

  /**
   * Create a new AI chat
   */
  async createAIChat(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const userId = req.user_id;
      if (!userId) {
        res.status(401).json({ success: false, message: 'Unauthorized' });
        return;
      }

      const { title } = req.body;
      const newChat = await this.userService.createAIChat(userId, title);

      if (!newChat) {
        res.status(404).json({ success: false, message: 'User not found' });
        return;
      }

      res.json({ success: true, chat: newChat });
    } catch (error) {
      logger.error({ error }, 'Error in createAIChat controller');
      res.status(500).json({ success: false, message: 'Internal server error' });
    }
  }

  /**
   * Send a message in an AI chat
   */
  async sendAIChatMessage(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const userId = req.user_id;
      if (!userId) {
        res.status(401).json({ success: false, message: 'Unauthorized' });
        return;
      }

      const chatId = req.params.chatId;
      const { message } = req.body;

      if (!message) {
        res.status(400).json({ success: false, message: 'Message is required' });
        return;
      }

      const updatedChat = await this.userService.sendAIChatMessage(userId, chatId, message);

      if (!updatedChat) {
        res.status(404).json({ success: false, message: 'Chat not found' });
        return;
      }

      res.json({ success: true, chat: updatedChat });
    } catch (error) {
      logger.error({ error }, 'Error in sendAIChatMessage controller');
      res.status(500).json({ success: false, message: 'Internal server error' });
    }
  }

  /**
   * Delete an AI chat
   */
  async deleteAIChat(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const userId = req.user_id;
      if (!userId) {
        res.status(401).json({ success: false, message: 'Unauthorized' });
        return;
      }

      const chatId = req.params.chatId;
      const result = await this.userService.deleteAIChat(userId, chatId);

      res.json(result);
    } catch (error) {
      logger.error({ error }, 'Error in deleteAIChat controller');
      res.status(500).json({ success: false, message: 'Internal server error' });
    }
  }

  /**
   * Add messages to AI chat history for a specific exam and QnA
   */
  async addAIChatMessages(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const userId = req.user_id;
      if (!userId) {
        res.status(401).json({ success: false, message: 'Unauthorized' });
        return;
      }

      const { examId, qnaId } = req.params;
      const { messages, quickReplies } = req.body;

      // Validate required parameters
      if (!examId || !qnaId) {
        res.status(400).json({
          success: false,
          message: 'examId and qnaId are required parameters'
        });
        return;
      }

      // Validate examId format (should be like "aws-certified-ai-practitioner-aif-c01")
      if (!examId.match(/^[a-z0-9-]+$/)) {
        res.status(400).json({
          success: false,
          message: 'Invalid examId format. Expected format: aws-certified-ai-practitioner-aif-c01'
        });
        return;
      }

      // Validate qnaId format (should be like "679e05c962fb0d0bbe484926")
      if (!qnaId.match(/^[a-f0-9]{24}$/)) {
        res.status(400).json({
          success: false,
          message: 'Invalid qnaId format. Expected 24-character hexadecimal string'
        });
        return;
      }

      // Validate messages array
      if (!messages || !Array.isArray(messages) || messages.length === 0) {
        res.status(400).json({
          success: false,
          message: 'messages array is required and must not be empty'
        });
        return;
      }

      // Validate message structure
      for (const message of messages) {
        if (!message.id || !message.choices || !Array.isArray(message.choices)) {
          res.status(400).json({
            success: false,
            message: 'Each message must have id and choices array'
          });
          return;
        }

        for (const choice of message.choices) {
          if (!choice.message || !choice.message.role || !choice.message.content) {
            res.status(400).json({
              success: false,
              message: 'Each choice must have message with role and content'
            });
            return;
          }

          if (!['user', 'assistant'].includes(choice.message.role)) {
            res.status(400).json({
              success: false,
              message: 'Message role must be either "user" or "assistant"'
            });
            return;
          }
        }
      }

      logger.debug('Adding AI chat messages', {
        userId,
        examId,
        qnaId,
        messageCount: messages.length,
        hasQuickReplies: !!quickReplies
      });

      const result = await this.userService.addAIChatMessages(userId, examId, qnaId, messages, quickReplies);

      if (result === null) {
        res.status(404).json({ success: false, message: 'User not found' });
        return;
      }

      res.json({
        success: true,
        messageCount: result.messageCount,
        addedCount: result.addedCount,
        examId,
        qnaId
      });
    } catch (error) {
      logger.error({ error }, 'Error in addAIChatMessages controller');
      res.status(500).json({ success: false, message: 'Internal server error' });
    }
  }

  /**
   * Get login history
   */
  async getLoginHistory(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const userId = req.user_id;
      if (!userId) {
        res.status(401).json({ success: false, message: 'Unauthorized' });
        return;
      }

      const limit = parseInt(req.query.limit as string || '10', 10);
      const offset = parseInt(req.query.offset as string || '0', 10);

      const loginHistory = await this.userService.getLoginHistory(userId, limit, offset);

      if (loginHistory === null) {
        res.status(404).json({ success: false, message: 'User not found' });
        return;
      }

      res.json({ success: true, ...loginHistory });
    } catch (error) {
      logger.error({ error }, 'Error in getLoginHistory controller');
      res.status(500).json({ success: false, message: 'Internal server error' });
    }
  }
}