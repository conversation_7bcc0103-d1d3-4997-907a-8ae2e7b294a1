import { Platform } from 'react-native';

/**
 * React Native Compression Utility
 * Provides client-side compression for large payloads like AI chat history
 * Uses LZ-string for cross-platform compatibility
 */

// Simple LZ-string implementation for React Native
class LZString {
  static _keyStr = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";

  static compressToBase64(input) {
    if (input == null) return "";
    let res = this._compress(input, 6, function(a) { return LZString._keyStr.charAt(a); });
    switch (res.length % 4) {
      default:
      case 0: return res;
      case 1: return res + "===";
      case 2: return res + "==";
      case 3: return res + "=";
    }
  }

  static decompressFromBase64(input) {
    if (input == null) return "";
    if (input == "") return null;
    return this._decompress(input.length, 32, function(index) {
      return LZString._getBaseValue(LZString._keyStr, input.charAt(index));
    });
  }

  static _compress(uncompressed, bitsPerChar, getCharFromInt) {
    if (uncompressed == null) return "";
    let i, value,
        context_dictionary = {},
        context_dictionaryToCreate = {},
        context_c = "",
        context_wc = "",
        context_w = "",
        context_enlargeIn = 2,
        context_dictSize = 3,
        context_numBits = 2,
        context_data = [],
        context_data_val = 0,
        context_data_position = 0,
        ii;

    for (ii = 0; ii < uncompressed.length; ii += 1) {
      context_c = uncompressed.charAt(ii);
      if (!Object.prototype.hasOwnProperty.call(context_dictionary, context_c)) {
        context_dictionary[context_c] = context_dictSize++;
        context_dictionaryToCreate[context_c] = true;
      }

      context_wc = context_w + context_c;
      if (Object.prototype.hasOwnProperty.call(context_dictionary, context_wc)) {
        context_w = context_wc;
      } else {
        if (Object.prototype.hasOwnProperty.call(context_dictionaryToCreate, context_w)) {
          if (context_w.charCodeAt(0) < 256) {
            for (i = 0; i < context_numBits; i++) {
              context_data_val = (context_data_val << 1);
              if (context_data_position == bitsPerChar - 1) {
                context_data_position = 0;
                context_data.push(getCharFromInt(context_data_val));
                context_data_val = 0;
              } else {
                context_data_position++;
              }
            }
            value = context_w.charCodeAt(0);
            for (i = 0; i < 8; i++) {
              context_data_val = (context_data_val << 1) | (value & 1);
              if (context_data_position == bitsPerChar - 1) {
                context_data_position = 0;
                context_data.push(getCharFromInt(context_data_val));
                context_data_val = 0;
              } else {
                context_data_position++;
              }
              value = value >> 1;
            }
          } else {
            value = 1;
            for (i = 0; i < context_numBits; i++) {
              context_data_val = (context_data_val << 1) | value;
              if (context_data_position == bitsPerChar - 1) {
                context_data_position = 0;
                context_data.push(getCharFromInt(context_data_val));
                context_data_val = 0;
              } else {
                context_data_position++;
              }
              value = 0;
            }
            value = context_w.charCodeAt(0);
            for (i = 0; i < 16; i++) {
              context_data_val = (context_data_val << 1) | (value & 1);
              if (context_data_position == bitsPerChar - 1) {
                context_data_position = 0;
                context_data.push(getCharFromInt(context_data_val));
                context_data_val = 0;
              } else {
                context_data_position++;
              }
              value = value >> 1;
            }
          }
          context_enlargeIn--;
          if (context_enlargeIn == 0) {
            context_enlargeIn = Math.pow(2, context_numBits);
            context_numBits++;
          }
          delete context_dictionaryToCreate[context_w];
        } else {
          value = context_dictionary[context_w];
          for (i = 0; i < context_numBits; i++) {
            context_data_val = (context_data_val << 1) | (value & 1);
            if (context_data_position == bitsPerChar - 1) {
              context_data_position = 0;
              context_data.push(getCharFromInt(context_data_val));
              context_data_val = 0;
            } else {
              context_data_position++;
            }
            value = value >> 1;
          }
        }
        context_enlargeIn--;
        if (context_enlargeIn == 0) {
          context_enlargeIn = Math.pow(2, context_numBits);
          context_numBits++;
        }
        context_dictionary[context_wc] = context_dictSize++;
        context_w = String(context_c);
      }
    }

    if (context_w !== "") {
      if (Object.prototype.hasOwnProperty.call(context_dictionaryToCreate, context_w)) {
        if (context_w.charCodeAt(0) < 256) {
          for (i = 0; i < context_numBits; i++) {
            context_data_val = (context_data_val << 1);
            if (context_data_position == bitsPerChar - 1) {
              context_data_position = 0;
              context_data.push(getCharFromInt(context_data_val));
              context_data_val = 0;
            } else {
              context_data_position++;
            }
          }
          value = context_w.charCodeAt(0);
          for (i = 0; i < 8; i++) {
            context_data_val = (context_data_val << 1) | (value & 1);
            if (context_data_position == bitsPerChar - 1) {
              context_data_position = 0;
              context_data.push(getCharFromInt(context_data_val));
              context_data_val = 0;
            } else {
              context_data_position++;
            }
            value = value >> 1;
          }
        } else {
          value = 1;
          for (i = 0; i < context_numBits; i++) {
            context_data_val = (context_data_val << 1) | value;
            if (context_data_position == bitsPerChar - 1) {
              context_data_position = 0;
              context_data.push(getCharFromInt(context_data_val));
              context_data_val = 0;
            } else {
              context_data_position++;
            }
            value = 0;
          }
          value = context_w.charCodeAt(0);
          for (i = 0; i < 16; i++) {
            context_data_val = (context_data_val << 1) | (value & 1);
            if (context_data_position == bitsPerChar - 1) {
              context_data_position = 0;
              context_data.push(getCharFromInt(context_data_val));
              context_data_val = 0;
            } else {
              context_data_position++;
            }
            value = value >> 1;
          }
        }
        context_enlargeIn--;
        if (context_enlargeIn == 0) {
          context_enlargeIn = Math.pow(2, context_numBits);
          context_numBits++;
        }
        delete context_dictionaryToCreate[context_w];
      } else {
        value = context_dictionary[context_w];
        for (i = 0; i < context_numBits; i++) {
          context_data_val = (context_data_val << 1) | (value & 1);
          if (context_data_position == bitsPerChar - 1) {
            context_data_position = 0;
            context_data.push(getCharFromInt(context_data_val));
            context_data_val = 0;
          } else {
            context_data_position++;
          }
          value = value >> 1;
        }
      }
      context_enlargeIn--;
      if (context_enlargeIn == 0) {
        context_enlargeIn = Math.pow(2, context_numBits);
        context_numBits++;
      }
    }

    value = 2;
    for (i = 0; i < context_numBits; i++) {
      context_data_val = (context_data_val << 1) | (value & 1);
      if (context_data_position == bitsPerChar - 1) {
        context_data_position = 0;
        context_data.push(getCharFromInt(context_data_val));
        context_data_val = 0;
      } else {
        context_data_position++;
      }
      value = value >> 1;
    }

    while (true) {
      context_data_val = (context_data_val << 1);
      if (context_data_position == bitsPerChar - 1) {
        context_data.push(getCharFromInt(context_data_val));
        break;
      } else context_data_position++;
    }
    return context_data.join('');
  }

  static _decompress(length, resetValue, getNextValue) {
    let dictionary = [],
        next,
        enlargeIn = 4,
        dictSize = 4,
        numBits = 3,
        entry = "",
        result = [],
        i,
        w,
        bits, resb, maxpower, power,
        c,
        data = {val: getNextValue(0), position: resetValue, index: 1};

    for (i = 0; i < 3; i += 1) {
      dictionary[i] = i;
    }

    bits = 0;
    maxpower = Math.pow(2, 2);
    power = 1;
    while (power != maxpower) {
      resb = data.val & data.position;
      data.position >>= 1;
      if (data.position == 0) {
        data.position = resetValue;
        data.val = getNextValue(data.index++);
      }
      bits |= (resb > 0 ? 1 : 0) * power;
      power <<= 1;
    }

    switch (next = bits) {
      case 0:
        bits = 0;
        maxpower = Math.pow(2, 8);
        power = 1;
        while (power != maxpower) {
          resb = data.val & data.position;
          data.position >>= 1;
          if (data.position == 0) {
            data.position = resetValue;
            data.val = getNextValue(data.index++);
          }
          bits |= (resb > 0 ? 1 : 0) * power;
          power <<= 1;
        }
        c = String.fromCharCode(bits);
        break;
      case 1:
        bits = 0;
        maxpower = Math.pow(2, 16);
        power = 1;
        while (power != maxpower) {
          resb = data.val & data.position;
          data.position >>= 1;
          if (data.position == 0) {
            data.position = resetValue;
            data.val = getNextValue(data.index++);
          }
          bits |= (resb > 0 ? 1 : 0) * power;
          power <<= 1;
        }
        c = String.fromCharCode(bits);
        break;
      case 2:
        return "";
    }
    dictionary[3] = c;
    w = c;
    result.push(c);
    while (true) {
      if (data.index > length) {
        return "";
      }

      bits = 0;
      maxpower = Math.pow(2, numBits);
      power = 1;
      while (power != maxpower) {
        resb = data.val & data.position;
        data.position >>= 1;
        if (data.position == 0) {
          data.position = resetValue;
          data.val = getNextValue(data.index++);
        }
        bits |= (resb > 0 ? 1 : 0) * power;
        power <<= 1;
      }

      switch (c = bits) {
        case 0:
          bits = 0;
          maxpower = Math.pow(2, 8);
          power = 1;
          while (power != maxpower) {
            resb = data.val & data.position;
            data.position >>= 1;
            if (data.position == 0) {
              data.position = resetValue;
              data.val = getNextValue(data.index++);
            }
            bits |= (resb > 0 ? 1 : 0) * power;
            power <<= 1;
          }

          dictionary[dictSize++] = String.fromCharCode(bits);
          c = dictSize - 1;
          enlargeIn--;
          break;
        case 1:
          bits = 0;
          maxpower = Math.pow(2, 16);
          power = 1;
          while (power != maxpower) {
            resb = data.val & data.position;
            data.position >>= 1;
            if (data.position == 0) {
              data.position = resetValue;
              data.val = getNextValue(data.index++);
            }
            bits |= (resb > 0 ? 1 : 0) * power;
            power <<= 1;
          }
          dictionary[dictSize++] = String.fromCharCode(bits);
          c = dictSize - 1;
          enlargeIn--;
          break;
        case 2:
          return result.join('');
      }

      if (enlargeIn == 0) {
        enlargeIn = Math.pow(2, numBits);
        numBits++;
      }

      if (dictionary[c]) {
        entry = dictionary[c];
      } else {
        if (c === dictSize) {
          entry = w + w.charAt(0);
        } else {
          return null;
        }
      }
      result.push(entry);

      dictionary[dictSize++] = w + entry.charAt(0);
      enlargeIn--;

      w = entry;

      if (enlargeIn == 0) {
        enlargeIn = Math.pow(2, numBits);
        numBits++;
      }
    }
  }

  static _getBaseValue(alphabet, character) {
    if (!LZString._baseReverseDic) LZString._baseReverseDic = {};
    if (!LZString._baseReverseDic[alphabet]) {
      LZString._baseReverseDic[alphabet] = {};
      for (let i = 0; i < alphabet.length; i++) {
        LZString._baseReverseDic[alphabet][alphabet.charAt(i)] = i;
      }
    }
    return LZString._baseReverseDic[alphabet][character];
  }
}

/**
 * React Native Compression Utility
 */
export class CompressionUtil {

  /**
   * Check if compression is enabled via environment variables
   * Note: In React Native, you'll need to set these at build time or use a config file
   */
  static isCompressionEnabled() {
    // Check if compression is explicitly disabled
    // You can set this via your build process or config
    if (typeof global !== 'undefined' && global.__COMPRESSION_DISABLED__) {
      return false;
    }

    // Default to enabled
    return true;
  }

  /**
   * Get compression threshold from configuration
   */
  static getCompressionThreshold() {
    // You can set this via your build process or config
    if (typeof global !== 'undefined' && global.__COMPRESSION_THRESHOLD__) {
      return global.__COMPRESSION_THRESHOLD__;
    }

    return 1024; // Default 1KB threshold
  }

  /**
   * Log compression configuration
   */
  static logCompressionConfig() {
    console.log('[CompressionUtil] Configuration:', {
      compressionEnabled: this.isCompressionEnabled(),
      compressionThreshold: this.getCompressionThreshold(),
      compressionThresholdFormatted: this.formatBytes(this.getCompressionThreshold())
    });
  }

  /**
   * Compress data for transmission
   * @param {any} data - Data to compress
   * @param {number} threshold - Size threshold in bytes (uses environment setting if not provided)
   * @returns {Object} Compression result
   */
  static compressData(data, threshold) {
    try {
      // Check if compression is enabled
      if (!this.isCompressionEnabled()) {
        console.log('[CompressionUtil] Compression is disabled via configuration');
        const jsonString = typeof data === 'string' ? data : JSON.stringify(data);
        const originalSize = new Blob([jsonString]).size;

        return {
          data: data,
          compressed: false,
          originalSize,
          compressedSize: originalSize,
          compressionRatio: 0
        };
      }

      const jsonString = typeof data === 'string' ? data : JSON.stringify(data);
      const originalSize = new Blob([jsonString]).size;

      // Use provided threshold or get from environment
      const actualThreshold = threshold !== undefined ? threshold : this.getCompressionThreshold();

      // Only compress if data exceeds threshold
      if (originalSize <= actualThreshold) {
        return {
          data: data,
          compressed: false,
          originalSize,
          compressedSize: originalSize,
          compressionRatio: 0
        };
      }

      const compressed = LZString.compressToBase64(jsonString);
      const compressedSize = new Blob([compressed]).size;
      const compressionRatio = ((originalSize - compressedSize) / originalSize) * 100;

      console.log(`[CompressionUtil] Compressed data: ${this.formatBytes(originalSize)} → ${this.formatBytes(compressedSize)} (${compressionRatio.toFixed(2)}% reduction)`);

      return {
        data: compressed,
        compressed: true,
        originalSize,
        compressedSize,
        compressionRatio,
        algorithm: 'lz-string'
      };
    } catch (error) {
      console.error('[CompressionUtil] Compression failed:', error);
      // Return original data if compression fails
      return {
        data: data,
        compressed: false,
        originalSize: new Blob([typeof data === 'string' ? data : JSON.stringify(data)]).size,
        compressedSize: new Blob([typeof data === 'string' ? data : JSON.stringify(data)]).size,
        compressionRatio: 0,
        error: error.message
      };
    }
  }

  /**
   * Decompress data received from server
   * @param {string} compressedData - Compressed data
   * @returns {any} Decompressed data
   */
  static decompressData(compressedData) {
    try {
      const decompressed = LZString.decompressFromBase64(compressedData);
      if (decompressed === null) {
        throw new Error('Decompression returned null');
      }

      try {
        return JSON.parse(decompressed);
      } catch {
        // If JSON parsing fails, return as string
        return decompressed;
      }
    } catch (error) {
      console.error('[CompressionUtil] Decompression failed:', error);
      throw new Error(`Decompression failed: ${error.message}`);
    }
  }

  /**
   * Check if data should be compressed
   * @param {any} data - Data to check
   * @param {number} threshold - Size threshold in bytes (uses environment setting if not provided)
   * @returns {boolean} Whether to compress
   */
  static shouldCompress(data, threshold) {
    // Check if compression is enabled first
    if (!this.isCompressionEnabled()) {
      return false;
    }

    // Use provided threshold or get from environment
    const actualThreshold = threshold !== undefined ? threshold : this.getCompressionThreshold();

    const jsonString = typeof data === 'string' ? data : JSON.stringify(data);
    const size = new Blob([jsonString]).size;
    return size > actualThreshold;
  }

  /**
   * Get data size in bytes
   * @param {any} data - Data to measure
   * @returns {number} Size in bytes
   */
  static getDataSize(data) {
    const jsonString = typeof data === 'string' ? data : JSON.stringify(data);
    return new Blob([jsonString]).size;
  }

  /**
   * Format bytes to human readable string
   * @param {number} bytes - Number of bytes
   * @returns {string} Formatted string
   */
  static formatBytes(bytes) {
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}

export default CompressionUtil;
