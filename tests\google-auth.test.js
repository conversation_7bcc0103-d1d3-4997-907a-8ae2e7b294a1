const axios = require('axios');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config({ path: '.env.development' });

const API_BASE_URL = process.env.API_URL || 'http://localhost:3016';
const TEST_TOKEN = process.env.TEST_TOKEN || 'test-token';

// Test configuration
const testConfig = {
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
};

// Create axios instance for testing
const testClient = axios.create(testConfig);

// Test utilities
const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms));

const logTestResult = (testName, success, details = {}) => {
  const status = success ? '✅ PASS' : '❌ FAIL';
  console.log(`${status} ${testName}`);
  if (details.error) {
    console.log(`   Error: ${details.error}`);
  }
  if (details.response) {
    console.log(`   Response: ${JSON.stringify(details.response, null, 2)}`);
  }
  console.log('');
};

// Test cases
async function testGoogleTokenValidation() {
  console.log('🧪 Testing Google Token Validation\n');

  // Test 1: Valid test token
  try {
    const response = await testClient.post('/api/auth/google', {
      idToken: TEST_TOKEN
    });

    const success = response.data.success && response.data.token;
    logTestResult('Google login with test token', success, {
      response: response.data
    });

    if (success) {
      return response.data.token;
    }
  } catch (error) {
    logTestResult('Google login with test token', false, {
      error: error.response?.data?.message || error.message
    });
  }

  return null;
}

async function testTokenAuthentication(token) {
  console.log('🔐 Testing Token Authentication\n');

  if (!token) {
    logTestResult('Token authentication (no token)', false, {
      error: 'No token provided from previous test'
    });
    return;
  }

  // Test 2: Validate token
  try {
    const response = await testClient.get('/api/auth/validate-token', {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    const success = response.data.success;
    logTestResult('Token validation', success, {
      response: response.data
    });
  } catch (error) {
    logTestResult('Token validation', false, {
      error: error.response?.data?.message || error.message
    });
  }

  // Test 3: Access protected endpoint
  try {
    const response = await testClient.get('/api/user/profile', {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    const success = response.data.success;
    logTestResult('Access protected endpoint (user profile)', success, {
      response: response.data
    });
  } catch (error) {
    logTestResult('Access protected endpoint (user profile)', false, {
      error: error.response?.data?.message || error.message
    });
  }
}

async function testUserByGoogleId(token) {
  console.log('👤 Testing User Retrieval by Google ID\n');

  if (!token) {
    logTestResult('Get user by Google ID (no token)', false, {
      error: 'No token provided from previous test'
    });
    return;
  }

  // Test 4: Get user by Google ID
  try {
    const testGoogleId = 'test-google-id';
    const response = await testClient.get(`/api/user/google/${testGoogleId}`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    const success = response.data.success && response.data.user;
    logTestResult('Get user by Google ID', success, {
      response: response.data
    });
  } catch (error) {
    logTestResult('Get user by Google ID', false, {
      error: error.response?.data?.message || error.message
    });
  }
}

async function testInvalidToken() {
  console.log('🚫 Testing Invalid Token Handling\n');

  // Test 5: Invalid token format
  try {
    const response = await testClient.get('/api/user/profile', {
      headers: {
        'Authorization': 'Bearer invalid-token-format'
      }
    });

    // Should not reach here
    logTestResult('Invalid token handling', false, {
      error: 'Expected authentication to fail but it succeeded'
    });
  } catch (error) {
    const success = error.response?.status === 401 || error.response?.status === 403;
    logTestResult('Invalid token handling', success, {
      response: error.response?.data
    });
  }

  // Test 6: No token
  try {
    const response = await testClient.get('/api/user/profile');

    // Should not reach here
    logTestResult('No token handling', false, {
      error: 'Expected authentication to fail but it succeeded'
    });
  } catch (error) {
    const success = error.response?.status === 401;
    logTestResult('No token handling', success, {
      response: error.response?.data
    });
  }
}

async function testTokenCaching(token) {
  console.log('⚡ Testing Token Caching\n');

  if (!token) {
    logTestResult('Token caching test (no token)', false, {
      error: 'No token provided from previous test'
    });
    return;
  }

  // Test 7: Multiple requests to test caching
  const startTime = Date.now();
  const requests = [];

  for (let i = 0; i < 5; i++) {
    requests.push(
      testClient.get('/api/user/profile', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })
    );
  }

  try {
    const responses = await Promise.all(requests);
    const endTime = Date.now();
    const totalTime = endTime - startTime;

    const allSuccessful = responses.every(response => response.data.success);
    logTestResult('Token caching (5 concurrent requests)', allSuccessful, {
      response: {
        totalTime: `${totalTime}ms`,
        averageTime: `${totalTime / 5}ms`,
        allSuccessful
      }
    });
  } catch (error) {
    logTestResult('Token caching (5 concurrent requests)', false, {
      error: error.response?.data?.message || error.message
    });
  }
}

async function testLogout(token) {
  console.log('🚪 Testing Logout\n');

  if (!token) {
    logTestResult('Logout test (no token)', false, {
      error: 'No token provided from previous test'
    });
    return;
  }

  // Test 8: Logout
  try {
    const response = await testClient.post('/auth/logout', {}, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    const success = response.data.success;
    logTestResult('Logout', success, {
      response: response.data
    });

    // Test 9: Try to use token after logout
    await delay(1000); // Wait for cache invalidation

    try {
      const profileResponse = await testClient.get('/user/profile', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      // Should not reach here
      logTestResult('Token invalidation after logout', false, {
        error: 'Token should be invalid after logout but request succeeded'
      });
    } catch (error) {
      const success = error.response?.status === 401 || error.response?.status === 403;
      logTestResult('Token invalidation after logout', success, {
        response: error.response?.data
      });
    }
  } catch (error) {
    logTestResult('Logout', false, {
      error: error.response?.data?.message || error.message
    });
  }
}

async function testServerEndpoints() {
  console.log('🌐 Testing Server Endpoints\n');

  // Test 10: Test endpoint
  try {
    const response = await testClient.get('/auth/test');
    const success = response.data.success;
    logTestResult('Auth test endpoint', success, {
      response: response.data
    });
  } catch (error) {
    logTestResult('Auth test endpoint', false, {
      error: error.response?.data?.message || error.message
    });
  }

  // Test 11: Test POST endpoint
  try {
    const response = await testClient.post('/auth/test', {
      testData: 'Hello World'
    });
    const success = response.data.success;
    logTestResult('Auth test POST endpoint', success, {
      response: response.data
    });
  } catch (error) {
    logTestResult('Auth test POST endpoint', false, {
      error: error.response?.data?.message || error.message
    });
  }
}

// Main test runner
async function runAllTests() {
  console.log('🚀 Starting Google Token Authentication Tests\n');
  console.log(`API Base URL: ${API_BASE_URL}`);
  console.log(`Test Token: ${TEST_TOKEN}\n`);

  try {
    // Run tests in sequence
    await testServerEndpoints();
    const token = await testGoogleTokenValidation();
    await testTokenAuthentication(token);
    await testUserByGoogleId(token);
    await testInvalidToken();
    await testTokenCaching(token);
    await testLogout(token);

    console.log('✨ All tests completed!\n');
  } catch (error) {
    console.error('💥 Test runner error:', error);
  }
}

// Export for use in other test files
module.exports = {
  testGoogleTokenValidation,
  testTokenAuthentication,
  testUserByGoogleId,
  testInvalidToken,
  testTokenCaching,
  testLogout,
  testServerEndpoints,
  runAllTests
};

// Run tests if this file is executed directly
if (require.main === module) {
  runAllTests();
}
